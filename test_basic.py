#!/usr/bin/env python3
"""
Basic test script to verify the application components
"""

import sys
import json
from datetime import datetime

def test_imports():
    """Test that all modules can be imported"""
    print("Testing imports...")
    
    try:
        from models import EPGRequest, EPGResponse, EPGProgram, Channel
        print("✅ Models imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import models: {e}")
        return False
    
    try:
        from api_client import SuperSportAPIClient
        print("✅ API client imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import API client: {e}")
        return False
    
    try:
        from neo4j_client import Neo4jEPGClient
        print("✅ Neo4j client imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import Neo4j client: {e}")
        return False
    
    try:
        from config import config
        print("✅ Config imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import config: {e}")
        return False
    
    return True


def test_models():
    """Test model validation"""
    print("\nTesting models...")
    
    try:
        from models import EPGRequest, Channel, EPGProgram
        
        # Test EPG Request
        request = EPGRequest(
            start_date_time="2025-01-20",
            end_date_time="2025-01-21"
        )
        print(f"✅ EPG Request created: {request.to_query_params()}")
        
        # Test Channel
        channel_data = {
            "stream": "test-stream",
            "channelCode": "TEST",
            "name": "Test Channel",
            "id": "test-id",
            "channelNumber": 100
        }
        channel = Channel(**channel_data)
        print(f"✅ Channel created: {channel.name}")
        
        return True
        
    except Exception as e:
        print(f"❌ Model test failed: {e}")
        return False


def test_sample_data_parsing():
    """Test parsing of sample EPG data"""
    print("\nTesting sample data parsing...")
    
    try:
        from models import EPGProgram
        
        # Load sample data
        with open('sample_epg_data.json', 'r', encoding='utf-8') as f:
            sample_data = json.load(f)
        
        if not sample_data:
            print("❌ No sample data found")
            return False
        
        # Parse first program
        first_program = EPGProgram(**sample_data[0])
        print(f"✅ Parsed sample program: {first_program.title}")
        print(f"   Sport: {first_program.sport}")
        print(f"   Channel: {first_program.channel[0].name if first_program.channel else 'Unknown'}")
        print(f"   Duration: {first_program.duration_minutes} minutes")
        
        # Test parsing all programs
        programs = [EPGProgram(**program_data) for program_data in sample_data]
        print(f"✅ Successfully parsed {len(programs)} programs from sample data")
        
        return True
        
    except Exception as e:
        print(f"❌ Sample data parsing failed: {e}")
        return False


def test_config():
    """Test configuration loading"""
    print("\nTesting configuration...")
    
    try:
        from config import Config
        
        # Test loading config (will use defaults if .env doesn't exist)
        config = Config.load()
        print(f"✅ Config loaded successfully")
        print(f"   Neo4j URI: {config.neo4j.uri}")
        print(f"   API URL: {config.api.base_url}")
        print(f"   Log Level: {config.app.log_level}")
        
        return True
        
    except Exception as e:
        print(f"❌ Config test failed: {e}")
        return False


def main():
    """Run all tests"""
    print("Running basic functionality tests...\n")
    
    tests = [
        test_imports,
        test_models,
        test_sample_data_parsing,
        test_config
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The application is ready to use.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
