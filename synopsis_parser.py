import re
from typing import List, Optional, Dict, Any
from dataclasses import dataclass
from pydantic import BaseModel


@dataclass
class ParsedSynopsis:
    """Parsed synopsis data with extracted entities"""
    teams: List[str]
    tournament: Optional[str]
    venue: Optional[str]
    location: Optional[str]
    age_group: Optional[str]
    episode_info: Optional[str]
    people: List[str]
    cricket_teams: List[str]  # IPL teams and abbreviations
    cricket_venues: List[str]  # Cricket stadiums
    cricket_people: List[str]  # Cricket players/personalities
    football_teams: List[str]  # Football teams (PSL, international)
    football_venues: List[str]  # Football stadiums
    football_people: List[str]  # Football players/personalities
    match_format: Optional[str]  # T20, ODI, Test, Quarter-final, etc.
    cricket_locations: List[str]  # Cricket-specific cities/countries
    football_locations: List[str]  # Football-specific cities/countries
    original_synopsis: str


class SynopsisParser:
    """Parser for extracting structured data from EPG synopsis"""

    def __init__(self):
        # Regex patterns for different data types
        self.patterns = {
            # Teams pattern: "Team A vs Team B" or "Team A v Team B"
            'teams': re.compile(r"([A-Za-z\s&']+?)\s+(?:vs?\.?|v)\s+([A-Za-z\s&']+?)(?:'|\.|$)", re.IGNORECASE),

            # Cricket team abbreviations (IPL, international teams)
            'cricket_teams': re.compile(r"\b(MI|DC|CSK|RCB|KKR|PBKS|RR|SRH|GT|LSG|Mumbai\s+Indians|Delhi\s+Capitals|Chennai\s+Super\s+Kings|Royal\s+Challengers\s+Bangalore|Kolkata\s+Knight\s+Riders|Punjab\s+Kings|Rajasthan\s+Royals|Sunrisers\s+Hyderabad|Gujarat\s+Titans|Lucknow\s+Super\s+Giants)\b", re.IGNORECASE),

            # Football teams (South African and international)
            'football_teams': re.compile(r"\b(Mamelodi\s+Sundowns|Orlando\s+Pirates|Kaizer\s+Chiefs|SuperSport\s+United|Cape\s+Town\s+City|AmaZulu|Stellenbosch\s+FC|Royal\s+AM|Sekhukhune\s+United|TS\s+Galaxy|Chippa\s+United|Maritzburg\s+United|Golden\s+Arrows|Baroka\s+FC|Black\s+Leopards|Esperance|Al\s+Ahly|Wydad\s+Casablanca|Raja\s+Casablanca|TP\s+Mazembe|Simba\s+SC|Zamalek|ES\s+Tunis|CS\s+Sfaxien|Sundowns|Pirates|Chiefs)\b", re.IGNORECASE),

            # Tournament/Competition patterns (enhanced for cricket and football)
            'tournament': re.compile(r"'([^']*(?:IPL|Indian\s+Premier\s+League|T20|Cup|Series|League|Championship|Derby|Open|Tournament|World\s+Cup|Champions\s+Trophy|Test\s+Series|CAF\s+Champions\s+League|UEFA\s+Champions\s+League|Premier\s+League|DStv\s+Premiership)[^']*)'", re.IGNORECASE),

            # Cricket-specific tournaments
            'cricket_tournament': re.compile(r"\b(IPL|Indian\s+Premier\s+League|T20\s+World\s+Cup|ODI\s+World\s+Cup|Champions\s+Trophy|Test\s+Series|Ashes|Border-Gavaskar\s+Trophy)\b", re.IGNORECASE),

            # Football-specific tournaments
            'football_tournament': re.compile(r"\b(CAF\s+Champions\s+League|TotalEnergies\s+CAF\s+Champions\s+League|UEFA\s+Champions\s+League|Premier\s+League|DStv\s+Premiership|FIFA\s+World\s+Cup|UEFA\s+Euro|AFCON|Africa\s+Cup\s+of\s+Nations|Nedbank\s+Cup|MTN8|Telkom\s+Knockout)\b", re.IGNORECASE),

            # Venue pattern: "From [Venue] - [Location]"
            'venue_location': re.compile(r"From\s+([^-]+?)\s*-\s*([^.]+)", re.IGNORECASE),

            # Cricket venues (stadiums)
            'cricket_venues': re.compile(r"\b(Wankhede\s+Stadium|Eden\s+Gardens|M\.\s*A\.\s*Chidambaram\s+Stadium|Feroz\s+Shah\s+Kotla|Chinnaswamy\s+Stadium|Rajiv\s+Gandhi\s+International\s+Stadium|Narendra\s+Modi\s+Stadium|Arun\s+Jaitley\s+Stadium|Lords|The\s+Oval|Old\s+Trafford|Headingley|Trent\s+Bridge|Edgbaston)\b", re.IGNORECASE),

            # Football venues (stadiums)
            'football_venues': re.compile(r"\b(Loftus\s+Versfeld|FNB\s+Stadium|Soccer\s+City|Ellis\s+Park|Orlando\s+Stadium|DHL\s+Stadium|Moses\s+Mabhida\s+Stadium|Peter\s+Mokaba\s+Stadium|Royal\s+Bafokeng\s+Stadium|Old\s+Trafford|Wembley|Emirates\s+Stadium|Anfield|Stamford\s+Bridge|Etihad\s+Stadium|Camp\s+Nou|Santiago\s+Bernabeu|Allianz\s+Arena|San\s+Siro)\b", re.IGNORECASE),

            # Age group patterns: U19A, U14, 1st XI, etc.
            'age_group': re.compile(r"\b(U\d+[A-Z]?|1st\s+XI|2nd\s+XI|\d+th\s+XI)\b", re.IGNORECASE),

            # Episode information: S25/E16 of 51
            'episode': re.compile(r"'(S\d+/E\d+\s+of\s+\d+[^']*)'", re.IGNORECASE),

            # People names (capitalized words that could be names)
            'people': re.compile(r"\b([A-Z][a-z]+\s+[A-Z][a-z]+)\b"),

            # Cricket players and personalities
            'cricket_people': re.compile(r"\b(Virat\s+Kohli|MS\s+Dhoni|Rohit\s+Sharma|KL\s+Rahul|Hardik\s+Pandya|Jasprit\s+Bumrah|Ravindra\s+Jadeja|Rishabh\s+Pant|Shikhar\s+Dhawan|Suryakumar\s+Yadav)\b", re.IGNORECASE),

            # Football players and personalities
            'football_people': re.compile(r"\b(Percy\s+Tau|Themba\s+Zwane|Khama\s+Billiat|Keagan\s+Dolly|Bradley\s+Grobler|Thembinkosi\s+Lorch|Vincent\s+Pule|Itumeleng\s+Khune|Ronwen\s+Williams|Denis\s+Onyango|Pitso\s+Mosimane|Rhulani\s+Mokwena|Gavin\s+Hunt|Stuart\s+Baxter|Benni\s+McCarthy)\b", re.IGNORECASE),

            # School/Institution names
            'schools': re.compile(r"\b([A-Z][a-z]*(?:\s+[A-Z][a-z]*)*\s+(?:High\s+School|College|School|University|Gimnasium|Kollege))\b"),

            # Match types and formats
            'match_format': re.compile(r"\b(T20|ODI|Test|Twenty20|One\s+Day\s+International|Test\s+Match|Highlights|HL|Quarter-final|Semi-final|Final|Group\s+Stage|Round\s+of\s+16|1st\s+Leg|2nd\s+Leg)\b", re.IGNORECASE),

            # Cities and locations (cricket-specific)
            'cricket_locations': re.compile(r"\b(Mumbai|Delhi|Chennai|Bangalore|Kolkata|Hyderabad|Ahmedabad|Jaipur|Mohali|Lucknow|Pune|Indore|Dharamshala|Guwahati|India|England|Australia|South\s+Africa|New\s+Zealand|Pakistan|Sri\s+Lanka|Bangladesh|West\s+Indies)\b", re.IGNORECASE),

            # Football-specific locations
            'football_locations': re.compile(r"\b(Pretoria|Johannesburg|Cape\s+Town|Durban|Bloemfontein|Port\s+Elizabeth|Polokwane|Nelspruit|Rustenburg|Soweto|South\s+Africa|Tunisia|Egypt|Morocco|Algeria|Nigeria|Ghana|Senegal|Cameroon|Ivory\s+Coast|Kenya|Tanzania|Uganda|Zambia|Zimbabwe|England|Spain|Germany|France|Italy|Brazil|Argentina)\b", re.IGNORECASE),
        }

    def parse_synopsis(self, synopsis: str) -> ParsedSynopsis:
        """Parse synopsis and extract structured data"""

        # Extract teams
        teams = self._extract_teams(synopsis)

        # Extract tournament
        tournament = self._extract_tournament(synopsis)

        # Extract venue and location
        venue, location = self._extract_venue_location(synopsis)

        # Extract age group
        age_group = self._extract_age_group(synopsis)

        # Extract episode information
        episode_info = self._extract_episode_info(synopsis)

        # Extract people names
        people = self._extract_people(synopsis)

        # Extract cricket-specific information
        cricket_teams = self._extract_cricket_teams(synopsis)
        cricket_venues = self._extract_cricket_venues(synopsis)
        cricket_people = self._extract_cricket_people(synopsis)
        cricket_locations = self._extract_cricket_locations(synopsis)

        # Extract football-specific information
        football_teams = self._extract_football_teams(synopsis)
        football_venues = self._extract_football_venues(synopsis)
        football_people = self._extract_football_people(synopsis)
        football_locations = self._extract_football_locations(synopsis)

        # Extract match format (common for both sports)
        match_format = self._extract_match_format(synopsis)

        return ParsedSynopsis(
            teams=teams,
            tournament=tournament,
            venue=venue,
            location=location,
            age_group=age_group,
            episode_info=episode_info,
            people=people,
            cricket_teams=cricket_teams,
            cricket_venues=cricket_venues,
            cricket_people=cricket_people,
            football_teams=football_teams,
            football_venues=football_venues,
            football_people=football_people,
            match_format=match_format,
            cricket_locations=cricket_locations,
            football_locations=football_locations,
            original_synopsis=synopsis
        )

    def _extract_teams(self, synopsis: str) -> List[str]:
        """Extract team names from synopsis"""
        teams = []

        # First try the vs/v pattern within quotes
        quoted_content = re.search(r"'([^']*)'", synopsis)
        if quoted_content:
            quoted_text = quoted_content.group(1)
            match = self.patterns['teams'].search(quoted_text)
            if match:
                team1 = match.group(1).strip()
                team2 = match.group(2).strip()
                teams = [team1, team2]

        # If no vs pattern found, try to extract school names as teams
        if not teams:
            school_matches = self.patterns['schools'].findall(synopsis)
            teams = [school.strip() for school in school_matches[:2]]  # Limit to 2

        # Clean up team names
        cleaned_teams = []
        for team in teams:
            # Remove quotes, extra spaces, and common prefixes
            team = re.sub(r"['\"]", "", team).strip()
            team = re.sub(r"^(School\s+|SCH\s+)", "", team, flags=re.IGNORECASE)

            # Filter out common false positives
            if (team and len(team) > 3 and
                not team.startswith("From ") and
                not any(word in team.lower() for word in ["cup", "series", "derby", "tournament"])):
                cleaned_teams.append(team)

        return cleaned_teams[:2]  # Limit to 2 teams for vs matches

    def _extract_tournament(self, synopsis: str) -> Optional[str]:
        """Extract tournament/competition name"""
        match = self.patterns['tournament'].search(synopsis)
        if match:
            tournament = match.group(1).strip()
            # Clean up common prefixes/suffixes
            tournament = re.sub(r"^(School\s+|SCH\s+)", "", tournament, flags=re.IGNORECASE)
            return tournament
        return None

    def _extract_venue_location(self, synopsis: str) -> tuple[Optional[str], Optional[str]]:
        """Extract venue and location"""
        match = self.patterns['venue_location'].search(synopsis)
        if match:
            venue = match.group(1).strip()
            location = match.group(2).strip()
            return venue, location
        return None, None

    def _extract_age_group(self, synopsis: str) -> Optional[str]:
        """Extract age group or category"""
        match = self.patterns['age_group'].search(synopsis)
        if match:
            return match.group(1).strip()
        return None

    def _extract_episode_info(self, synopsis: str) -> Optional[str]:
        """Extract episode information"""
        match = self.patterns['episode'].search(synopsis)
        if match:
            return match.group(1).strip()
        return None

    def _extract_people(self, synopsis: str) -> List[str]:
        """Extract people names"""
        matches = self.patterns['people'].findall(synopsis)
        # Filter out common false positives
        excluded_words = {
            'High School', 'South Africa', 'Cape Town', 'New Zealand',
            'United States', 'Live From', 'From The', 'King Price',
            'Derby Series', 'Curro Halala', 'Under Armour', 'Century City',
            'From Wits', 'From Grey', 'From Curro', 'From Eunice',
            'Boden Cup', 'Mamelodi Sundowns'
        }

        people = []
        for name in matches:
            if (name not in excluded_words and
                not any(word in name for word in ['School', 'College', 'University', 'High', 'Cup', 'From', 'City']) and
                not name.endswith(' Ridge') and
                not name.endswith(' Bay')):
                people.append(name.strip())

        return list(set(people))  # Remove duplicates

    def _extract_cricket_teams(self, synopsis: str) -> List[str]:
        """Extract cricket team names and abbreviations"""
        matches = self.patterns['cricket_teams'].findall(synopsis)
        # Convert abbreviations to full names for consistency
        team_mapping = {
            'MI': 'Mumbai Indians',
            'DC': 'Delhi Capitals',
            'CSK': 'Chennai Super Kings',
            'RCB': 'Royal Challengers Bangalore',
            'KKR': 'Kolkata Knight Riders',
            'PBKS': 'Punjab Kings',
            'RR': 'Rajasthan Royals',
            'SRH': 'Sunrisers Hyderabad',
            'GT': 'Gujarat Titans',
            'LSG': 'Lucknow Super Giants'
        }

        teams = []
        for match in matches:
            team_name = team_mapping.get(match.upper(), match)
            teams.append(team_name)

        return list(set(teams))  # Remove duplicates

    def _extract_cricket_venues(self, synopsis: str) -> List[str]:
        """Extract cricket venue names"""
        matches = self.patterns['cricket_venues'].findall(synopsis)
        return list(set(matches))  # Remove duplicates

    def _extract_cricket_people(self, synopsis: str) -> List[str]:
        """Extract cricket player and personality names"""
        matches = self.patterns['cricket_people'].findall(synopsis)
        return list(set(matches))  # Remove duplicates

    def _extract_match_format(self, synopsis: str) -> Optional[str]:
        """Extract match format (T20, ODI, Test, etc.)"""
        match = self.patterns['match_format'].search(synopsis)
        if match:
            format_name = match.group(1).strip()
            # Normalize format names
            format_mapping = {
                'HL': 'Highlights',
                'T20': 'Twenty20',
                'ODI': 'One Day International'
            }
            return format_mapping.get(format_name.upper(), format_name)
        return None

    def _extract_cricket_locations(self, synopsis: str) -> List[str]:
        """Extract cricket-specific locations"""
        matches = self.patterns['cricket_locations'].findall(synopsis)
        return list(set(matches))  # Remove duplicates

    def _extract_football_teams(self, synopsis: str) -> List[str]:
        """Extract football team names"""
        matches = self.patterns['football_teams'].findall(synopsis)
        # Normalize team names for consistency
        team_mapping = {
            'Sundowns': 'Mamelodi Sundowns',
            'Pirates': 'Orlando Pirates',
            'Chiefs': 'Kaizer Chiefs'
        }

        teams = []
        for match in matches:
            team_name = team_mapping.get(match, match)
            teams.append(team_name)

        return list(set(teams))  # Remove duplicates

    def _extract_football_venues(self, synopsis: str) -> List[str]:
        """Extract football venue names"""
        matches = self.patterns['football_venues'].findall(synopsis)
        return list(set(matches))  # Remove duplicates

    def _extract_football_people(self, synopsis: str) -> List[str]:
        """Extract football player and personality names"""
        matches = self.patterns['football_people'].findall(synopsis)
        return list(set(matches))  # Remove duplicates

    def _extract_football_locations(self, synopsis: str) -> List[str]:
        """Extract football-specific locations"""
        matches = self.patterns['football_locations'].findall(synopsis)
        return list(set(matches))  # Remove duplicates


# Enhanced Neo4j node models for extracted entities
class TeamNode(BaseModel):
    """Neo4j Team/School node model"""
    name: str
    type: str = "team"  # team, school, institution


class TournamentNode(BaseModel):
    """Neo4j Tournament/Competition node model"""
    name: str
    type: str = "tournament"  # tournament, cup, series, league


class VenueNode(BaseModel):
    """Neo4j Venue node model"""
    name: str
    location: Optional[str] = None


class LocationNode(BaseModel):
    """Neo4j Location node model"""
    name: str
    type: str = "location"  # city, country, region


class PersonNode(BaseModel):
    """Neo4j Person node model"""
    name: str
    type: str = "person"  # player, presenter, commentator


class AgeGroupNode(BaseModel):
    """Neo4j Age Group node model"""
    name: str
    type: str = "age_group"  # U19A, 1st XI, etc.


class EpisodeNode(BaseModel):
    """Neo4j Episode node model"""
    info: str
    season: Optional[str] = None
    episode: Optional[str] = None
    total_episodes: Optional[str] = None


# Enhanced relationship models
class CompetesInRelationship(BaseModel):
    """COMPETES_IN relationship between Team and Tournament"""
    pass


class PlaysAtRelationship(BaseModel):
    """PLAYS_AT relationship between Program and Venue"""
    pass


class LocatedInRelationship(BaseModel):
    """LOCATED_IN relationship between Venue and Location"""
    pass


class FeaturesRelationship(BaseModel):
    """FEATURES relationship between Program and Person"""
    role: Optional[str] = None  # player, presenter, etc.


class HasAgeGroupRelationship(BaseModel):
    """HAS_AGE_GROUP relationship between Program and AgeGroup"""
    pass


class PartOfSeriesRelationship(BaseModel):
    """PART_OF_SERIES relationship between Program and Episode"""
    pass


def test_synopsis_parser():
    """Test the synopsis parser with sample data"""
    parser = SynopsisParser()

    test_cases = [
        "'School Hockey - 1st XI: Eunice High School vs C & N Sekondêre Meisieskool Oranje'. From Eunice High School for Girls - Bloemfontein, South Africa.",
        "'Aitken & Boden Cup - Trinityhouse Randpark Ridge vs St Stithians College'. From Wits University - Johannesburg, South Africa.",
        "'Curro Halala Cup - Milnerton High School vs Camps Bay High School'. LIVE From Curro Century City High School - Cape Town, South Africa.",
        "'S25/E16 of 51'. We bring you bigger personalities, tougher challenges, and the hottest esports action from the ASL and AUL leagues.",
        "'S25/E4 of 6 - Under Armour Shadow 3'. Mamelodi Sundowns defender Kegan Johannes and GDL star Samkelo Mthkelombeni join the team from Football on 216.",
        "'King Price Derby Series - Grey College vs Afrikaanse Hoër Seunskool'. From Grey College - Bloemfontein, South Africa.",
        "'Tata Indian Premier League T20 Highlights - Mumbai Indians vs Delhi Capitals'. From Wankhede Stadium - Mumbai, India.",
        "'IPL HL '25: MI v DC'. From Wankhede Stadium - Mumbai, India.",
        "'T20 World Cup 2024: India vs Australia'. From Melbourne Cricket Ground - Melbourne, Australia.",
        "'TotalEnergies CAF Champions League Blitz Highlights - Quarter-final 1st Leg: Mamelodi Sundowns vs Esperance'. From Loftus Versfeld - Pretoria, South Africa.",
        "'DStv Premiership: Orlando Pirates vs Kaizer Chiefs'. From FNB Stadium - Johannesburg, South Africa."
    ]

    for i, synopsis in enumerate(test_cases, 1):
        print(f"\nTest Case {i}:")
        print(f"Synopsis: {synopsis}")

        parsed = parser.parse_synopsis(synopsis)
        print(f"Teams: {parsed.teams}")
        print(f"Tournament: {parsed.tournament}")
        print(f"Venue: {parsed.venue}")
        print(f"Location: {parsed.location}")
        print(f"Age Group: {parsed.age_group}")
        print(f"Episode: {parsed.episode_info}")
        print(f"People: {parsed.people}")
        print(f"Cricket Teams: {parsed.cricket_teams}")
        print(f"Cricket Venues: {parsed.cricket_venues}")
        print(f"Cricket People: {parsed.cricket_people}")
        print(f"Football Teams: {parsed.football_teams}")
        print(f"Football Venues: {parsed.football_venues}")
        print(f"Football People: {parsed.football_people}")
        print(f"Match Format: {parsed.match_format}")
        print(f"Cricket Locations: {parsed.cricket_locations}")
        print(f"Football Locations: {parsed.football_locations}")


if __name__ == "__main__":
    test_synopsis_parser()
