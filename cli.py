#!/usr/bin/env python3
"""
CLI utility for EPG Data Ingestion
"""

import argparse
import logging
import sys
import json

from config import config
from api_client import SuperSportAPIClient, create_epg_request
from neo4j_client import Neo4jEPGClient


def setup_logging(level: str = "INFO"):
    """Setup logging"""
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )


def test_api_connection():
    """Test API connection"""
    print("Testing SuperSport API connection...")
    client = SuperSportAPIClient(base_url=config.api.base_url)

    if client.test_connection():
        print("✅ API connection successful")
        return True
    else:
        print("❌ API connection failed")
        return False


def test_neo4j_connection():
    """Test Neo4j connection"""
    print("Testing Neo4j connection...")
    try:
        client = Neo4jEPGClient(
            uri=config.neo4j.uri,
            username=config.neo4j.username,
            password=config.neo4j.password,
            database=config.neo4j.database
        )

        if client.test_connection():
            print("✅ Neo4j connection successful")
            client.close()
            return True
        else:
            print("❌ Neo4j connection failed")
            client.close()
            return False
    except Exception as e:
        print(f"❌ Neo4j connection failed: {e}")
        return False


def fetch_sample_data(start_date: str, end_date: str, output_file: str = None):
    """Fetch sample EPG data"""
    print(f"Fetching EPG data from {start_date} to {end_date}...")

    client = SuperSportAPIClient(base_url=config.api.base_url)
    request = create_epg_request(
        start_date_time=start_date,
        end_date_time=end_date
    )

    response = client.fetch_epg_data(request)

    if response:
        print(f"✅ Fetched {len(response.programs)} programs")

        if output_file:
            data = [program.model_dump() for program in response.programs]
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False, default=str)
            print(f"💾 Saved to {output_file}")

        # Show sample data
        if response.programs:
            sample = response.programs[0]
            print(f"\nSample program:")
            print(f"  Title: {sample.title}")
            print(f"  Sport: {sample.sport}")
            print(f"  Channel: {sample.channel[0].name if sample.channel else 'Unknown'}")
            print(f"  Start: {sample.start}")
            print(f"  Duration: {sample.duration_minutes} minutes")

        return True
    else:
        print("❌ Failed to fetch data")
        return False


def get_database_stats():
    """Get database statistics"""
    print("Getting database statistics...")

    try:
        client = Neo4jEPGClient(
            uri=config.neo4j.uri,
            username=config.neo4j.username,
            password=config.neo4j.password,
            database=config.neo4j.database
        )

        stats = client.get_statistics()
        client.close()

        print("📊 Enhanced Database Statistics:")
        print(f"  Total Nodes: {stats.get('total_nodes', 0)}")
        print(f"  Total Programs: {stats.get('total_programs', 0)}")
        print(f"  Total Channels: {stats.get('total_channels', 0)}")
        print(f"  Total Relationships: {stats.get('total_relationships', 0)}")
        print(f"  BROADCASTS Relationships: {stats.get('broadcasts_relationships', 0)}")
        print(f"  AIRED_ON Relationships: {stats.get('aired_on_relationships', 0)}")

        print("\n🏷️ Node Labels:")
        labels = stats.get('node_labels', [])
        if labels and isinstance(labels, list) and len(labels) > 0:
            label_list = labels[0].get('labels', []) if isinstance(labels[0], dict) else []
            for label in label_list:
                print(f"  - {label}")

        print("\n📊 Node Counts by Label:")
        sample_nodes = stats.get('sample_nodes', [])
        for node in sample_nodes:
            if isinstance(node, dict):
                print(f"  {node.get('label', 'Unknown')}: {node.get('count', 0)} nodes")

        print("\n📺 Channel Program Counts:")
        channel_counts = stats.get('channel_program_counts', [])
        for channel in channel_counts:
            if isinstance(channel, dict):
                channel_name = channel.get('channel_name', 'Unknown')
                channel_number = channel.get('channel_number', 'N/A')
                program_count = channel.get('program_count', 0)
                print(f"  {channel_name} (Ch {channel_number}): {program_count} programs")

        return True

    except Exception as e:
        print(f"❌ Failed to get statistics: {e}")
        return False


def search_programs(search_term: str, limit: int = 10):
    """Search programs"""
    print(f"Searching for programs containing '{search_term}'...")

    try:
        client = Neo4jEPGClient(
            uri=config.neo4j.uri,
            username=config.neo4j.username,
            password=config.neo4j.password,
            database=config.neo4j.database
        )

        results = client.search_programs(search_term, limit)
        client.close()

        if results:
            print(f"🔍 Found {len(results)} programs:")
            for i, program in enumerate(results, 1):
                print(f"  {i}. {program['title']}")
                print(f"     Sport: {program['sport']}")
                print(f"     Start: {program['start_time']}")
                print(f"     Synopsis: {program['synopsis'][:100]}...")
                print()
        else:
            print("No programs found")

        return True

    except Exception as e:
        print(f"❌ Search failed: {e}")
        return False


def clear_database():
    """Clear all data from database"""
    print("⚠️  This will delete ALL EPG data from the database!")
    confirm = input("Type 'yes' to confirm: ")

    if confirm.lower() != 'yes':
        print("Operation cancelled")
        return False

    try:
        client = Neo4jEPGClient(
            uri=config.neo4j.uri,
            username=config.neo4j.username,
            password=config.neo4j.password,
            database=config.neo4j.database
        )

        client.clear_all_data()
        client.close()

        print("✅ Database cleared successfully")
        return True

    except Exception as e:
        print(f"❌ Failed to clear database: {e}")
        return False


def main():
    """Main CLI entry point"""
    parser = argparse.ArgumentParser(description="EPG Data Ingestion CLI")
    parser.add_argument("--log-level", default="INFO", help="Log level")

    subparsers = parser.add_subparsers(dest="command", help="Available commands")

    # Test connections
    subparsers.add_parser("test-api", help="Test API connection")
    subparsers.add_parser("test-neo4j", help="Test Neo4j connection")
    subparsers.add_parser("test-all", help="Test all connections")

    # Data operations
    fetch_parser = subparsers.add_parser("fetch", help="Fetch sample EPG data")
    fetch_parser.add_argument("--start-date", required=True, help="Start date (YYYY-MM-DD)")
    fetch_parser.add_argument("--end-date", required=True, help="End date (YYYY-MM-DD)")
    fetch_parser.add_argument("--output", help="Output file for JSON data")

    # Database operations
    subparsers.add_parser("stats", help="Get database statistics")

    search_parser = subparsers.add_parser("search", help="Search programs")
    search_parser.add_argument("term", help="Search term")
    search_parser.add_argument("--limit", type=int, default=10, help="Result limit")

    subparsers.add_parser("clear", help="Clear all data from database")

    args = parser.parse_args()

    if not args.command:
        parser.print_help()
        return

    setup_logging(args.log_level)

    # Execute commands
    success = True

    if args.command == "test-api":
        success = test_api_connection()
    elif args.command == "test-neo4j":
        success = test_neo4j_connection()
    elif args.command == "test-all":
        success = test_api_connection() and test_neo4j_connection()
    elif args.command == "fetch":
        success = fetch_sample_data(args.start_date, args.end_date, args.output)
    elif args.command == "stats":
        success = get_database_stats()
    elif args.command == "search":
        success = search_programs(args.term, args.limit)
    elif args.command == "clear":
        success = clear_database()

    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
