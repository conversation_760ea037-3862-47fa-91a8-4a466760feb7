#!/usr/bin/env python3
"""
CLI utility for EPG Data Ingestion
"""

import argparse
import logging
import sys
import json

from config import config
from api_client import SuperSportAPIClient, create_epg_request
from neo4j_client import Neo4jEPGClient


def setup_logging(level: str = "INFO"):
    """Setup logging"""
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )


def test_api_connection():
    """Test API connection"""
    print("Testing SuperSport API connection...")
    client = SuperSportAPIClient(base_url=config.api.base_url)

    if client.test_connection():
        print("✅ API connection successful")
        return True
    else:
        print("❌ API connection failed")
        return False


def test_neo4j_connection():
    """Test Neo4j connection"""
    print("Testing Neo4j connection...")
    try:
        client = Neo4jEPGClient(
            uri=config.neo4j.uri,
            username=config.neo4j.username,
            password=config.neo4j.password,
            database=config.neo4j.database
        )

        if client.test_connection():
            print("✅ Neo4j connection successful")
            client.close()
            return True
        else:
            print("❌ Neo4j connection failed")
            client.close()
            return False
    except Exception as e:
        print(f"❌ Neo4j connection failed: {e}")
        return False


def fetch_sample_data(start_date: str, end_date: str, output_file: str = None):
    """Fetch sample EPG data"""
    print(f"Fetching EPG data from {start_date} to {end_date}...")

    client = SuperSportAPIClient(base_url=config.api.base_url)
    request = create_epg_request(
        start_date_time=start_date,
        end_date_time=end_date
    )

    response = client.fetch_epg_data(request)

    if response:
        print(f"✅ Fetched {len(response.programs)} programs")

        if output_file:
            data = [program.model_dump() for program in response.programs]
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False, default=str)
            print(f"💾 Saved to {output_file}")

        # Show sample data
        if response.programs:
            sample = response.programs[0]
            print(f"\nSample program:")
            print(f"  Title: {sample.title}")
            print(f"  Sport: {sample.sport}")
            print(f"  Channel: {sample.channel[0].name if sample.channel else 'Unknown'}")
            print(f"  Start: {sample.start}")
            print(f"  Duration: {sample.duration_minutes} minutes")

        return True
    else:
        print("❌ Failed to fetch data")
        return False


def get_database_stats():
    """Get database statistics"""
    print("Getting database statistics...")

    try:
        client = Neo4jEPGClient(
            uri=config.neo4j.uri,
            username=config.neo4j.username,
            password=config.neo4j.password,
            database=config.neo4j.database
        )

        stats = client.get_statistics()
        client.close()

        print("📊 Database Statistics:")
        print(f"  Channels: {stats.get('total_channels', 0)}")
        print(f"  Programs: {stats.get('total_programs', 0)}")
        print(f"  Sports: {stats.get('total_sports', 0)}")
        print(f"  Genres: {stats.get('total_genres', 0)}")
        print(f"  Packages: {stats.get('total_packages', 0)}")
        print(f"  Teams: {stats.get('total_teams', 0)}")
        print(f"  Tournaments: {stats.get('total_tournaments', 0)}")
        print(f"  Venues: {stats.get('total_venues', 0)}")
        print(f"  Locations: {stats.get('total_locations', 0)}")
        print(f"  People: {stats.get('total_people', 0)}")
        print(f"  Age Groups: {stats.get('total_age_groups', 0)}")
        print(f"  Episodes: {stats.get('total_episodes', 0)}")
        print(f"  Relationships: {stats.get('total_relationships', 0)}")
        print(f"  Live Programs: {stats.get('live_programs', 0)}")

        print("\n🏆 Top Sports by Program Count:")
        for sport in stats.get('programs_by_sport', [])[:5]:
            print(f"  {sport['sport']}: {sport['count']} programs")

        print("\n📺 Top Channels by Program Count:")
        for channel in stats.get('channels_with_most_programs', [])[:5]:
            print(f"  {channel['channel']}: {channel['program_count']} programs")

        print("\n🏟️ Most Active Teams:")
        for team in stats.get('most_active_teams', [])[:5]:
            print(f"  {team['team']}: {team['tournaments']} tournaments")

        print("\n📍 Popular Venues:")
        for venue in stats.get('popular_venues', [])[:5]:
            location = f" ({venue['location']})" if venue['location'] else ""
            print(f"  {venue['venue']}{location}: {venue['programs']} programs")

        print("\n🏆 Tournaments by Team Count:")
        for tournament in stats.get('tournaments_by_programs', [])[:5]:
            print(f"  {tournament['tournament']}: {tournament['teams']} teams")

        return True

    except Exception as e:
        print(f"❌ Failed to get statistics: {e}")
        return False


def search_programs(search_term: str, limit: int = 10):
    """Search programs"""
    print(f"Searching for programs containing '{search_term}'...")

    try:
        client = Neo4jEPGClient(
            uri=config.neo4j.uri,
            username=config.neo4j.username,
            password=config.neo4j.password,
            database=config.neo4j.database
        )

        results = client.search_programs(search_term, limit)
        client.close()

        if results:
            print(f"🔍 Found {len(results)} programs:")
            for i, program in enumerate(results, 1):
                print(f"  {i}. {program['title']}")
                print(f"     Sport: {program['sport']}")
                print(f"     Start: {program['start_time']}")
                print(f"     Synopsis: {program['synopsis'][:100]}...")
                print()
        else:
            print("No programs found")

        return True

    except Exception as e:
        print(f"❌ Search failed: {e}")
        return False


def clear_database():
    """Clear all data from database"""
    print("⚠️  This will delete ALL EPG data from the database!")
    confirm = input("Type 'yes' to confirm: ")

    if confirm.lower() != 'yes':
        print("Operation cancelled")
        return False

    try:
        client = Neo4jEPGClient(
            uri=config.neo4j.uri,
            username=config.neo4j.username,
            password=config.neo4j.password,
            database=config.neo4j.database
        )

        client.clear_all_data()
        client.close()

        print("✅ Database cleared successfully")
        return True

    except Exception as e:
        print(f"❌ Failed to clear database: {e}")
        return False


def main():
    """Main CLI entry point"""
    parser = argparse.ArgumentParser(description="EPG Data Ingestion CLI")
    parser.add_argument("--log-level", default="INFO", help="Log level")

    subparsers = parser.add_subparsers(dest="command", help="Available commands")

    # Test connections
    subparsers.add_parser("test-api", help="Test API connection")
    subparsers.add_parser("test-neo4j", help="Test Neo4j connection")
    subparsers.add_parser("test-all", help="Test all connections")

    # Data operations
    fetch_parser = subparsers.add_parser("fetch", help="Fetch sample EPG data")
    fetch_parser.add_argument("--start-date", required=True, help="Start date (YYYY-MM-DD)")
    fetch_parser.add_argument("--end-date", required=True, help="End date (YYYY-MM-DD)")
    fetch_parser.add_argument("--output", help="Output file for JSON data")

    # Database operations
    subparsers.add_parser("stats", help="Get database statistics")

    search_parser = subparsers.add_parser("search", help="Search programs")
    search_parser.add_argument("term", help="Search term")
    search_parser.add_argument("--limit", type=int, default=10, help="Result limit")

    subparsers.add_parser("clear", help="Clear all data from database")

    args = parser.parse_args()

    if not args.command:
        parser.print_help()
        return

    setup_logging(args.log_level)

    # Execute commands
    success = True

    if args.command == "test-api":
        success = test_api_connection()
    elif args.command == "test-neo4j":
        success = test_neo4j_connection()
    elif args.command == "test-all":
        success = test_api_connection() and test_neo4j_connection()
    elif args.command == "fetch":
        success = fetch_sample_data(args.start_date, args.end_date, args.output)
    elif args.command == "stats":
        success = get_database_stats()
    elif args.command == "search":
        success = search_programs(args.term, args.limit)
    elif args.command == "clear":
        success = clear_database()

    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
