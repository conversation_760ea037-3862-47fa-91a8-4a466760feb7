#!/usr/bin/env python3
"""
Test script for the simplified Neo4j EPG client
"""

from neo4j_client import Neo4jEPGClient
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_simplified_client():
    """Test the simplified Neo4j client with sample data"""
    
    # Sample EPG data (as it would come from the API)
    sample_epg_data = [
        {
            "title": "Test Program 1",
            "name": "Test Program 1",
            "sport": "Football",
            "rating": "PG",
            "synopsis": "A test football match between Team A and Team B",
            "start": "12/25/2024 14:00:00",
            "end": "12/25/2024 16:00:00",
            "isLive": True,
            "showmax": False,
            "thumbnailUri": "https://example.com/thumb1.jpg",
            "channel": [
                {
                    "id": "ch1",
                    "name": "Sports Channel 1",
                    "channelCode": "SC1",
                    "channelNumber": 101,
                    "stream": "stream1",
                    "icon": "https://example.com/icon1.png"
                }
            ],
            "packages": ["Premium Sports", "Basic Package"],
            "subGenres": ["Live Sports", "Football"]
        },
        {
            "title": "Test Program 2", 
            "name": "Test Program 2",
            "sport": "Cricket",
            "rating": "G",
            "synopsis": "A test cricket match highlights",
            "start": "12/25/2024 18:00:00",
            "end": "12/25/2024 19:30:00",
            "isLive": False,
            "showmax": True,
            "thumbnailUri": "https://example.com/thumb2.jpg",
            "channel": [
                {
                    "id": "ch2",
                    "name": "Sports Channel 2", 
                    "channelCode": "SC2",
                    "channelNumber": 102,
                    "stream": "stream2",
                    "icon": "https://example.com/icon2.png"
                }
            ],
            "packages": ["Cricket Package"],
            "subGenres": ["Highlights", "Cricket"]
        }
    ]
    
    print("🧪 Testing Simplified Neo4j EPG Client")
    print("=" * 50)
    
    # Note: This test assumes you have Neo4j running locally
    # You may need to adjust the connection details
    try:
        client = Neo4jEPGClient(
            uri="bolt://localhost:7687",
            username="neo4j", 
            password="password",  # Change this to your actual password
            database="neo4j"
        )
        
        print("1. Testing connection...")
        if not client.test_connection():
            print("❌ Failed to connect to Neo4j. Please check your connection details.")
            return False
        print("✅ Connected to Neo4j successfully")
        
        print("\n2. Creating constraints and indexes...")
        client.create_constraints_and_indexes()
        print("✅ Constraints and indexes created")
        
        print("\n3. Clearing existing data...")
        client.clear_all_data()
        print("✅ Database cleared")
        
        print("\n4. Ingesting sample EPG data...")
        stats = client.ingest_epg_data(sample_epg_data)
        print(f"✅ Ingestion completed: {stats}")
        
        print("\n5. Getting database statistics...")
        db_stats = client.get_statistics()
        print("📊 Database Statistics:")
        for key, value in db_stats.items():
            print(f"  {key}: {value}")
        
        print("\n6. Searching for programs...")
        search_results = client.search_programs("Test", limit=5)
        print(f"🔍 Found {len(search_results)} programs containing 'Test':")
        for program in search_results:
            print(f"  - {program.get('title', 'Unknown')}")
        
        client.close()
        print("\n✅ All tests completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

if __name__ == "__main__":
    success = test_simplified_client()
    if success:
        print("\n🎉 Simplified Neo4j client is working correctly!")
        print("\nKey features:")
        print("- ✅ Direct data injection (key as label, value as property)")
        print("- ✅ Simplified node creation")
        print("- ✅ Progress tracking")
        print("- ✅ Basic statistics")
        print("- ✅ Program search")
        print("- ✅ No complex parsing or relationships")
    else:
        print("\n❌ Tests failed. Please check your Neo4j connection.")
