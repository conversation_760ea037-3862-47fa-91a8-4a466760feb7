#!/usr/bin/env python3
"""
Test script to demonstrate enhanced synopsis parsing for Cricket and Football
"""

from synopsis_parser import Syn<PERSON><PERSON><PERSON><PERSON>


def test_enhanced_synopsis_parsing():
    """Test the enhanced synopsis parser with Cricket and Football examples"""
    parser = SynopsisParser()
    
    print("=" * 80)
    print("Enhanced Synopsis Parsing Test - Cricket and Football")
    print("=" * 80)
    
    # Test cases from the JSON examples provided
    test_cases = [
        {
            "name": "IPL Cricket Example",
            "synopsis": "'Tata Indian Premier League T20 Highlights - Mumbai Indians vs Delhi Capitals'. From Wankhede Stadium - Mumbai, India.",
            "sport": "Cricket"
        },
        {
            "name": "CAF Champions League Football Example", 
            "synopsis": "'TotalEnergies CAF Champions League Blitz Highlights - Quarter-final 1st Leg: Mamelodi Sundowns vs Esperance'. From Loftus Versfeld - Pretoria, South Africa.",
            "sport": "Football"
        },
        {
            "name": "IPL Abbreviated Example",
            "synopsis": "'IPL HL '25: MI v DC'. From Wankhede Stadium - Mumbai, India.",
            "sport": "Cricket"
        },
        {
            "name": "PSL Football Example",
            "synopsis": "'DStv Premiership: Orlando Pirates vs Kaizer Chiefs'. From FNB Stadium - Johannesburg, South Africa.",
            "sport": "Football"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{'-' * 60}")
        print(f"Test Case {i}: {test_case['name']}")
        print(f"Sport: {test_case['sport']}")
        print(f"Synopsis: {test_case['synopsis']}")
        print(f"{'-' * 60}")
        
        parsed = parser.parse_synopsis(test_case['synopsis'])
        
        # General information
        print(f"📋 General Teams: {parsed.teams}")
        print(f"🏆 Tournament: {parsed.tournament}")
        print(f"🏟️  Venue: {parsed.venue}")
        print(f"📍 Location: {parsed.location}")
        print(f"⚽ Match Format: {parsed.match_format}")
        
        # Cricket-specific information
        if parsed.cricket_teams or parsed.cricket_venues or parsed.cricket_locations:
            print(f"\n🏏 Cricket Information:")
            print(f"   Teams: {parsed.cricket_teams}")
            print(f"   Venues: {parsed.cricket_venues}")
            print(f"   People: {parsed.cricket_people}")
            print(f"   Locations: {parsed.cricket_locations}")
        
        # Football-specific information
        if parsed.football_teams or parsed.football_venues or parsed.football_locations:
            print(f"\n⚽ Football Information:")
            print(f"   Teams: {parsed.football_teams}")
            print(f"   Venues: {parsed.football_venues}")
            print(f"   People: {parsed.football_people}")
            print(f"   Locations: {parsed.football_locations}")
        
        # Relationship suggestions
        print(f"\n🔗 Potential Neo4j Relationships:")
        relationships = []
        
        # Team relationships
        if parsed.cricket_teams:
            for team in parsed.cricket_teams:
                relationships.append(f"   (Program)-[:FEATURES_TEAM]->({team}:CricketTeam)")
        
        if parsed.football_teams:
            for team in parsed.football_teams:
                relationships.append(f"   (Program)-[:FEATURES_TEAM]->({team}:FootballTeam)")
        
        # Tournament relationships
        if parsed.tournament:
            relationships.append(f"   (Program)-[:PART_OF_TOURNAMENT]->({parsed.tournament}:Tournament)")
        
        # Venue relationships
        if parsed.venue:
            relationships.append(f"   (Program)-[:PLAYED_AT]->({parsed.venue}:Venue)")
            if parsed.location:
                relationships.append(f"   ({parsed.venue}:Venue)-[:LOCATED_IN]->({parsed.location}:Location)")
        
        # Cricket venue relationships
        if parsed.cricket_venues:
            for venue in parsed.cricket_venues:
                relationships.append(f"   (Program)-[:PLAYED_AT]->({venue}:CricketVenue)")
        
        # Football venue relationships
        if parsed.football_venues:
            for venue in parsed.football_venues:
                relationships.append(f"   (Program)-[:PLAYED_AT]->({venue}:FootballVenue)")
        
        # Location relationships
        if parsed.cricket_locations:
            for location in parsed.cricket_locations:
                relationships.append(f"   (Program)-[:LOCATED_IN]->({location}:CricketLocation)")
        
        if parsed.football_locations:
            for location in parsed.football_locations:
                relationships.append(f"   (Program)-[:LOCATED_IN]->({location}:FootballLocation)")
        
        # Match format relationships
        if parsed.match_format:
            relationships.append(f"   (Program)-[:HAS_FORMAT]->({parsed.match_format}:MatchFormat)")
        
        for relationship in relationships:
            print(relationship)
    
    print(f"\n{'=' * 80}")
    print("✅ Enhanced Synopsis Parsing Test Completed!")
    print("✅ Successfully extracted Cricket and Football specific entities")
    print("✅ Generated potential Neo4j relationships for knowledge graph")
    print("=" * 80)


if __name__ == "__main__":
    test_enhanced_synopsis_parsing()
