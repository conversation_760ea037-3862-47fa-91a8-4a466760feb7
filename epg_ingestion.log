2025-05-27 14:55:43,377 - __main__ - INFO - Starting EPG Data Ingestion
2025-05-27 14:55:43,378 - __main__ - ERROR - EPG Data Ingestion failed: Configuration errors: Neo4j password is required
Traceback (most recent call last):
  File "D:\Project\TATA_MCVR\Dev\EPG_DATA_INGESTION\main.py", line 31, in main
    config.validate_config()
  File "D:\Project\TATA_MCVR\Dev\EPG_DATA_INGESTION\config.py", line 111, in validate_config
    raise ValueError(f"Configuration errors: {', '.join(errors)}")
ValueError: Configuration errors: Neo4j password is required
2025-05-27 15:00:35,473 - __main__ - INFO - Starting EPG Data Ingestion
2025-05-27 15:00:35,475 - __main__ - INFO - Configuration validated successfully
2025-05-27 15:00:35,479 - __main__ - INFO - Testing API connection...
2025-05-27 15:00:37,097 - __main__ - INFO - API connection successful
2025-05-27 15:00:37,098 - __main__ - INFO - Testing Neo4j connection...
2025-05-27 15:00:37,699 - __main__ - INFO - Neo4j connection successful
2025-05-27 15:00:37,700 - __main__ - INFO - Setting up database constraints and indexes...
2025-05-27 15:00:37,795 - neo4j_client - INFO - Executed: CREATE CONSTRAINT channel_id_unique IF NOT EXISTS FOR (c:Channel) REQUIRE c.id IS UNIQUE
2025-05-27 15:00:37,795 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE CONSTRAINT channel_id_unique IF NOT EXISTS FOR (e:Channel) REQUIRE (e.id) IS UNIQUE` has no effect.} {description: `CONSTRAINT channel_id FOR (e:Channel) REQUIRE (e.id) IS UNIQUE` already exists.} {position: None} for query: 'CREATE CONSTRAINT channel_id_unique IF NOT EXISTS FOR (c:Channel) REQUIRE c.id IS UNIQUE'
2025-05-27 15:00:37,848 - neo4j_client - INFO - Executed: CREATE CONSTRAINT sport_name_unique IF NOT EXISTS FOR (s:Sport) REQUIRE s.name IS UNIQUE
2025-05-27 15:00:37,849 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE CONSTRAINT sport_name_unique IF NOT EXISTS FOR (e:Sport) REQUIRE (e.name) IS UNIQUE` has no effect.} {description: `CONSTRAINT sport_name FOR (e:Sport) REQUIRE (e.name) IS UNIQUE` already exists.} {position: None} for query: 'CREATE CONSTRAINT sport_name_unique IF NOT EXISTS FOR (s:Sport) REQUIRE s.name IS UNIQUE'
2025-05-27 15:00:37,898 - neo4j_client - INFO - Executed: CREATE CONSTRAINT genre_name_unique IF NOT EXISTS FOR (g:Genre) REQUIRE g.name IS UNIQUE
2025-05-27 15:00:37,899 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE CONSTRAINT genre_name_unique IF NOT EXISTS FOR (e:Genre) REQUIRE (e.name) IS UNIQUE` has no effect.} {description: `CONSTRAINT genre_name FOR (e:Genre) REQUIRE (e.name) IS UNIQUE` already exists.} {position: None} for query: 'CREATE CONSTRAINT genre_name_unique IF NOT EXISTS FOR (g:Genre) REQUIRE g.name IS UNIQUE'
2025-05-27 15:00:37,950 - neo4j_client - INFO - Executed: CREATE CONSTRAINT package_name_unique IF NOT EXISTS FOR (p:Package) REQUIRE p.name IS UNIQUE
2025-05-27 15:00:37,951 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE CONSTRAINT package_name_unique IF NOT EXISTS FOR (e:Package) REQUIRE (e.name) IS UNIQUE` has no effect.} {description: `CONSTRAINT package_name FOR (e:Package) REQUIRE (e.name) IS UNIQUE` already exists.} {position: None} for query: 'CREATE CONSTRAINT package_name_unique IF NOT EXISTS FOR (p:Package) REQUIRE p.name IS UNIQUE'
2025-05-27 15:00:38,006 - neo4j_client - INFO - Executed: CREATE CONSTRAINT team_name_unique IF NOT EXISTS FOR (t:Team) REQUIRE t.name IS UNIQUE
2025-05-27 15:00:38,007 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE CONSTRAINT team_name_unique IF NOT EXISTS FOR (e:Team) REQUIRE (e.name) IS UNIQUE` has no effect.} {description: `CONSTRAINT team_name FOR (e:Team) REQUIRE (e.name) IS UNIQUE` already exists.} {position: None} for query: 'CREATE CONSTRAINT team_name_unique IF NOT EXISTS FOR (t:Team) REQUIRE t.name IS UNIQUE'
2025-05-27 15:00:38,106 - neo4j_client - INFO - Executed: CREATE CONSTRAINT tournament_name_unique IF NOT EXISTS FOR (t:Tournament) REQUIRE t.name IS UNIQUE
2025-05-27 15:00:38,164 - neo4j_client - INFO - Executed: CREATE CONSTRAINT venue_name_unique IF NOT EXISTS FOR (v:Venue) REQUIRE v.name IS UNIQUE
2025-05-27 15:00:38,165 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE CONSTRAINT venue_name_unique IF NOT EXISTS FOR (e:Venue) REQUIRE (e.name) IS UNIQUE` has no effect.} {description: `CONSTRAINT venue_name FOR (e:Venue) REQUIRE (e.name) IS UNIQUE` already exists.} {position: None} for query: 'CREATE CONSTRAINT venue_name_unique IF NOT EXISTS FOR (v:Venue) REQUIRE v.name IS UNIQUE'
2025-05-27 15:00:38,221 - neo4j_client - INFO - Executed: CREATE CONSTRAINT location_name_unique IF NOT EXISTS FOR (l:Location) REQUIRE l.name IS UNIQUE
2025-05-27 15:00:38,222 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE CONSTRAINT location_name_unique IF NOT EXISTS FOR (e:Location) REQUIRE (e.name) IS UNIQUE` has no effect.} {description: `CONSTRAINT location_name FOR (e:Location) REQUIRE (e.name) IS UNIQUE` already exists.} {position: None} for query: 'CREATE CONSTRAINT location_name_unique IF NOT EXISTS FOR (l:Location) REQUIRE l.name IS UNIQUE'
2025-05-27 15:00:38,271 - neo4j_client - INFO - Executed: CREATE CONSTRAINT person_name_unique IF NOT EXISTS FOR (p:Person) REQUIRE p.name IS UNIQUE
2025-05-27 15:00:38,272 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE CONSTRAINT person_name_unique IF NOT EXISTS FOR (e:Person) REQUIRE (e.name) IS UNIQUE` has no effect.} {description: `CONSTRAINT person_name FOR (e:Person) REQUIRE (e.name) IS UNIQUE` already exists.} {position: None} for query: 'CREATE CONSTRAINT person_name_unique IF NOT EXISTS FOR (p:Person) REQUIRE p.name IS UNIQUE'
2025-05-27 15:00:38,323 - neo4j_client - INFO - Executed: CREATE CONSTRAINT age_group_name_unique IF NOT EXISTS FOR (a:AgeGroup) REQUIRE a.name IS UNIQUE
2025-05-27 15:00:38,324 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE CONSTRAINT age_group_name_unique IF NOT EXISTS FOR (e:AgeGroup) REQUIRE (e.name) IS UNIQUE` has no effect.} {description: `CONSTRAINT agegroup_name FOR (e:AgeGroup) REQUIRE (e.name) IS UNIQUE` already exists.} {position: None} for query: 'CREATE CONSTRAINT age_group_name_unique IF NOT EXISTS FOR (a:AgeGroup) REQUIRE a.name IS UNIQUE'
2025-05-27 15:00:38,378 - neo4j_client - INFO - Executed: CREATE INDEX channel_name_index IF NOT EXISTS FOR (c:Channel) ON (c.name)
2025-05-27 15:00:38,379 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX channel_name_index IF NOT EXISTS FOR (e:Channel) ON (e.name)` has no effect.} {description: `RANGE INDEX channel_name_idx FOR (e:Channel) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX channel_name_index IF NOT EXISTS FOR (c:Channel) ON (c.name)'
2025-05-27 15:00:38,433 - neo4j_client - INFO - Executed: CREATE INDEX program_title_index IF NOT EXISTS FOR (p:Program) ON (p.title)
2025-05-27 15:00:38,523 - neo4j_client - INFO - Executed: CREATE INDEX program_start_time_index IF NOT EXISTS FOR (p:Program) ON (p.start_time)
2025-05-27 15:00:38,610 - neo4j_client - INFO - Executed: CREATE INDEX program_sport_index IF NOT EXISTS FOR (p:Program) ON (p.sport)
2025-05-27 15:00:38,677 - neo4j_client - INFO - Executed: CREATE INDEX team_name_index IF NOT EXISTS FOR (t:Team) ON (t.name)
2025-05-27 15:00:38,678 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX team_name_index IF NOT EXISTS FOR (e:Team) ON (e.name)` has no effect.} {description: `RANGE INDEX team_name FOR (e:Team) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX team_name_index IF NOT EXISTS FOR (t:Team) ON (t.name)'
2025-05-27 15:00:38,729 - neo4j_client - INFO - Executed: CREATE INDEX tournament_name_index IF NOT EXISTS FOR (t:Tournament) ON (t.name)
2025-05-27 15:00:38,730 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX tournament_name_index IF NOT EXISTS FOR (e:Tournament) ON (e.name)` has no effect.} {description: `RANGE INDEX tournament_name_unique FOR (e:Tournament) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX tournament_name_index IF NOT EXISTS FOR (t:Tournament) ON (t.name)'
2025-05-27 15:00:38,781 - neo4j_client - INFO - Executed: CREATE INDEX venue_name_index IF NOT EXISTS FOR (v:Venue) ON (v.name)
2025-05-27 15:00:38,782 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX venue_name_index IF NOT EXISTS FOR (e:Venue) ON (e.name)` has no effect.} {description: `RANGE INDEX venue_name FOR (e:Venue) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX venue_name_index IF NOT EXISTS FOR (v:Venue) ON (v.name)'
2025-05-27 15:00:38,833 - neo4j_client - INFO - Executed: CREATE INDEX location_name_index IF NOT EXISTS FOR (l:Location) ON (l.name)
2025-05-27 15:00:38,834 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX location_name_index IF NOT EXISTS FOR (e:Location) ON (e.name)` has no effect.} {description: `RANGE INDEX location_name FOR (e:Location) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX location_name_index IF NOT EXISTS FOR (l:Location) ON (l.name)'
2025-05-27 15:00:38,884 - neo4j_client - INFO - Executed: CREATE INDEX person_name_index IF NOT EXISTS FOR (p:Person) ON (p.name)
2025-05-27 15:00:38,887 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX person_name_index IF NOT EXISTS FOR (e:Person) ON (e.name)` has no effect.} {description: `RANGE INDEX person_name FOR (e:Person) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX person_name_index IF NOT EXISTS FOR (p:Person) ON (p.name)'
2025-05-27 15:00:38,888 - __main__ - INFO - Fetching EPG data from 2025-05-27 to 2025-05-28
2025-05-27 15:00:38,888 - api_client - INFO - Fetching EPG data from https://supersport.com/apix/guide/v5.3/tvguide with params: {'countryCode': 'za', 'channelOnly': 'false', 'startDateTime': '2025-05-27', 'endDateTime': '2025-05-28', 'liveOnly': 'false'}
2025-05-27 15:00:40,053 - api_client - INFO - Successfully fetched 516 programs
2025-05-27 15:00:40,059 - __main__ - INFO - Fetched 516 programs
2025-05-27 15:00:40,059 - __main__ - INFO - Ingesting data into Neo4j...
2025-05-27 15:00:49,196 - __main__ - ERROR - EPG Data Ingestion failed: Existing exports of data: object cannot be re-sized
Traceback (most recent call last):
  File "D:\Project\TATA_MCVR\Dev\EPG_DATA_INGESTION\neo4j_client.py", line 142, in ingest_epg_data
    rel_count = self._create_relationships(session, program, program_id, parsed_synopsis)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Project\TATA_MCVR\Dev\EPG_DATA_INGESTION\neo4j_client.py", line 414, in _create_relationships
    relationship_count += self._create_enhanced_relationships(session, program_id, parsed_synopsis)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Project\TATA_MCVR\Dev\EPG_DATA_INGESTION\neo4j_client.py", line 526, in _create_enhanced_relationships
    session.run(query, {
  File "D:\Project\TATA_MCVR\Dev\EPG_DATA_INGESTION\.venv\Lib\site-packages\neo4j\_sync\work\session.py", line 328, in run
    self._auto_result._run(
  File "D:\Project\TATA_MCVR\Dev\EPG_DATA_INGESTION\.venv\Lib\site-packages\neo4j\_sync\work\result.py", line 236, in _run
    self._attach()
  File "D:\Project\TATA_MCVR\Dev\EPG_DATA_INGESTION\.venv\Lib\site-packages\neo4j\_sync\work\result.py", line 430, in _attach
    self._connection.fetch_message()
  File "D:\Project\TATA_MCVR\Dev\EPG_DATA_INGESTION\.venv\Lib\site-packages\neo4j\_sync\io\_common.py", line 184, in inner
    func(*args, **kwargs)
  File "D:\Project\TATA_MCVR\Dev\EPG_DATA_INGESTION\.venv\Lib\site-packages\neo4j\_sync\io\_bolt.py", line 861, in fetch_message
    tag, fields = self.inbox.pop(
                  ^^^^^^^^^^^^^^^
  File "D:\Project\TATA_MCVR\Dev\EPG_DATA_INGESTION\.venv\Lib\site-packages\neo4j\_sync\io\_common.py", line 77, in pop
    self._buffer_one_chunk()
  File "D:\Project\TATA_MCVR\Dev\EPG_DATA_INGESTION\.venv\Lib\site-packages\neo4j\_sync\io\_common.py", line 54, in _buffer_one_chunk
    receive_into_buffer(self._socket, self._buffer, 2)
  File "D:\Project\TATA_MCVR\Dev\EPG_DATA_INGESTION\.venv\Lib\site-packages\neo4j\_sync\io\_common.py", line 345, in receive_into_buffer
    n = sock.recv_into(
        ^^^^^^^^^^^^^^^
  File "D:\Project\TATA_MCVR\Dev\EPG_DATA_INGESTION\.venv\Lib\site-packages\neo4j\_async_compat\network\_bolt_socket.py", line 364, in recv_into
    return self._wait_for_io(self._socket.recv_into, buffer, nbytes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Project\TATA_MCVR\Dev\EPG_DATA_INGESTION\.venv\Lib\site-packages\neo4j\_async_compat\network\_bolt_socket.py", line 339, in _wait_for_io
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\ProgramData\anaconda3\Lib\ssl.py", line 1251, in recv_into
    return self.read(nbytes, buffer)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\ProgramData\anaconda3\Lib\ssl.py", line 1103, in read
    return self._sslobj.read(len, buffer)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Project\TATA_MCVR\Dev\EPG_DATA_INGESTION\.venv\Lib\site-packages\neo4j\_sync\work\session.py", line 222, in close
    self._connection.fetch_all()
  File "D:\Project\TATA_MCVR\Dev\EPG_DATA_INGESTION\.venv\Lib\site-packages\neo4j\_sync\io\_bolt.py", line 879, in fetch_all
    detail_delta, summary_delta = self.fetch_message()
                                  ^^^^^^^^^^^^^^^^^^^^
  File "D:\Project\TATA_MCVR\Dev\EPG_DATA_INGESTION\.venv\Lib\site-packages\neo4j\_sync\io\_bolt.py", line 861, in fetch_message
    tag, fields = self.inbox.pop(
                  ^^^^^^^^^^^^^^^
  File "D:\Project\TATA_MCVR\Dev\EPG_DATA_INGESTION\.venv\Lib\site-packages\neo4j\_sync\io\_common.py", line 77, in pop
    self._buffer_one_chunk()
  File "D:\Project\TATA_MCVR\Dev\EPG_DATA_INGESTION\.venv\Lib\site-packages\neo4j\_sync\io\_common.py", line 59, in _buffer_one_chunk
    receive_into_buffer(
  File "D:\Project\TATA_MCVR\Dev\EPG_DATA_INGESTION\.venv\Lib\site-packages\neo4j\_sync\io\_common.py", line 342, in receive_into_buffer
    buffer.data += bytearray(end - len(buffer.data))
BufferError: Existing exports of data: object cannot be re-sized

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Project\TATA_MCVR\Dev\EPG_DATA_INGESTION\main.py", line 80, in main
    stats = neo4j_client.ingest_epg_data(epg_response)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Project\TATA_MCVR\Dev\EPG_DATA_INGESTION\neo4j_client.py", line 106, in ingest_epg_data
    with self.driver.session(database=self.database) as session:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Project\TATA_MCVR\Dev\EPG_DATA_INGESTION\.venv\Lib\site-packages\neo4j\_sync\work\session.py", line 130, in __exit__
    self.close()
  File "D:\Project\TATA_MCVR\Dev\EPG_DATA_INGESTION\.venv\Lib\site-packages\neo4j\_sync\work\session.py", line 233, in close
    self._disconnect()
  File "D:\Project\TATA_MCVR\Dev\EPG_DATA_INGESTION\.venv\Lib\site-packages\neo4j\_sync\work\session.py", line 145, in _disconnect
    return super()._disconnect(sync=sync)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Project\TATA_MCVR\Dev\EPG_DATA_INGESTION\.venv\Lib\site-packages\neo4j\_sync\work\workspace.py", line 298, in _disconnect
    self._pool.release(self._connection)
  File "D:\Project\TATA_MCVR\Dev\EPG_DATA_INGESTION\.venv\Lib\site-packages\neo4j\_sync\io\_pool.py", line 481, in release
    connection.reset()
  File "D:\Project\TATA_MCVR\Dev\EPG_DATA_INGESTION\.venv\Lib\site-packages\neo4j\_sync\io\_bolt5.py", line 447, in reset
    self.fetch_all()
  File "D:\Project\TATA_MCVR\Dev\EPG_DATA_INGESTION\.venv\Lib\site-packages\neo4j\_sync\io\_bolt.py", line 879, in fetch_all
    detail_delta, summary_delta = self.fetch_message()
                                  ^^^^^^^^^^^^^^^^^^^^
  File "D:\Project\TATA_MCVR\Dev\EPG_DATA_INGESTION\.venv\Lib\site-packages\neo4j\_sync\io\_bolt.py", line 861, in fetch_message
    tag, fields = self.inbox.pop(
                  ^^^^^^^^^^^^^^^
  File "D:\Project\TATA_MCVR\Dev\EPG_DATA_INGESTION\.venv\Lib\site-packages\neo4j\_sync\io\_common.py", line 77, in pop
    self._buffer_one_chunk()
  File "D:\Project\TATA_MCVR\Dev\EPG_DATA_INGESTION\.venv\Lib\site-packages\neo4j\_sync\io\_common.py", line 59, in _buffer_one_chunk
    receive_into_buffer(
  File "D:\Project\TATA_MCVR\Dev\EPG_DATA_INGESTION\.venv\Lib\site-packages\neo4j\_sync\io\_common.py", line 342, in receive_into_buffer
    buffer.data += bytearray(end - len(buffer.data))
BufferError: Existing exports of data: object cannot be re-sized
2025-05-27 15:05:32,330 - __main__ - INFO - Starting EPG Data Ingestion
2025-05-27 15:05:32,330 - __main__ - INFO - Configuration validated successfully
2025-05-27 15:05:32,332 - __main__ - INFO - Testing API connection...
2025-05-27 15:05:33,745 - __main__ - INFO - API connection successful
2025-05-27 15:05:33,745 - __main__ - INFO - Testing Neo4j connection...
2025-05-27 15:05:34,745 - __main__ - INFO - Neo4j connection successful
2025-05-27 15:05:34,745 - __main__ - INFO - Setting up database constraints and indexes...
2025-05-27 15:05:34,865 - neo4j_client - INFO - Executed: CREATE CONSTRAINT channel_id_unique IF NOT EXISTS FOR (c:Channel) REQUIRE c.id IS UNIQUE
2025-05-27 15:05:35,016 - neo4j_client - INFO - Executed: CREATE CONSTRAINT sport_name_unique IF NOT EXISTS FOR (s:Sport) REQUIRE s.name IS UNIQUE
2025-05-27 15:05:35,164 - neo4j_client - INFO - Executed: CREATE CONSTRAINT genre_name_unique IF NOT EXISTS FOR (g:Genre) REQUIRE g.name IS UNIQUE
2025-05-27 15:05:35,264 - neo4j_client - INFO - Executed: CREATE CONSTRAINT package_name_unique IF NOT EXISTS FOR (p:Package) REQUIRE p.name IS UNIQUE
2025-05-27 15:05:35,350 - neo4j_client - INFO - Executed: CREATE CONSTRAINT team_name_unique IF NOT EXISTS FOR (t:Team) REQUIRE t.name IS UNIQUE
2025-05-27 15:05:35,427 - neo4j_client - INFO - Executed: CREATE CONSTRAINT tournament_name_unique IF NOT EXISTS FOR (t:Tournament) REQUIRE t.name IS UNIQUE
2025-05-27 15:05:35,512 - neo4j_client - INFO - Executed: CREATE CONSTRAINT venue_name_unique IF NOT EXISTS FOR (v:Venue) REQUIRE v.name IS UNIQUE
2025-05-27 15:05:35,591 - neo4j_client - INFO - Executed: CREATE CONSTRAINT location_name_unique IF NOT EXISTS FOR (l:Location) REQUIRE l.name IS UNIQUE
2025-05-27 15:05:35,670 - neo4j_client - INFO - Executed: CREATE CONSTRAINT person_name_unique IF NOT EXISTS FOR (p:Person) REQUIRE p.name IS UNIQUE
2025-05-27 15:05:35,749 - neo4j_client - INFO - Executed: CREATE CONSTRAINT age_group_name_unique IF NOT EXISTS FOR (a:AgeGroup) REQUIRE a.name IS UNIQUE
2025-05-27 15:05:35,805 - neo4j_client - INFO - Executed: CREATE INDEX channel_name_index IF NOT EXISTS FOR (c:Channel) ON (c.name)
2025-05-27 15:05:35,882 - neo4j_client - INFO - Executed: CREATE INDEX program_title_index IF NOT EXISTS FOR (p:Program) ON (p.title)
2025-05-27 15:05:35,955 - neo4j_client - INFO - Executed: CREATE INDEX program_start_time_index IF NOT EXISTS FOR (p:Program) ON (p.start_time)
2025-05-27 15:05:36,023 - neo4j_client - INFO - Executed: CREATE INDEX program_sport_index IF NOT EXISTS FOR (p:Program) ON (p.sport)
2025-05-27 15:05:36,097 - neo4j_client - INFO - Executed: CREATE INDEX team_name_index IF NOT EXISTS FOR (t:Team) ON (t.name)
2025-05-27 15:05:36,097 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX team_name_index IF NOT EXISTS FOR (e:Team) ON (e.name)` has no effect.} {description: `RANGE INDEX team_name_unique FOR (e:Team) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX team_name_index IF NOT EXISTS FOR (t:Team) ON (t.name)'
2025-05-27 15:05:36,149 - neo4j_client - INFO - Executed: CREATE INDEX tournament_name_index IF NOT EXISTS FOR (t:Tournament) ON (t.name)
2025-05-27 15:05:36,149 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX tournament_name_index IF NOT EXISTS FOR (e:Tournament) ON (e.name)` has no effect.} {description: `RANGE INDEX tournament_name_unique FOR (e:Tournament) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX tournament_name_index IF NOT EXISTS FOR (t:Tournament) ON (t.name)'
2025-05-27 15:05:36,202 - neo4j_client - INFO - Executed: CREATE INDEX venue_name_index IF NOT EXISTS FOR (v:Venue) ON (v.name)
2025-05-27 15:05:36,202 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX venue_name_index IF NOT EXISTS FOR (e:Venue) ON (e.name)` has no effect.} {description: `RANGE INDEX venue_name_unique FOR (e:Venue) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX venue_name_index IF NOT EXISTS FOR (v:Venue) ON (v.name)'
2025-05-27 15:05:36,252 - neo4j_client - INFO - Executed: CREATE INDEX location_name_index IF NOT EXISTS FOR (l:Location) ON (l.name)
2025-05-27 15:05:36,252 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX location_name_index IF NOT EXISTS FOR (e:Location) ON (e.name)` has no effect.} {description: `RANGE INDEX location_name_unique FOR (e:Location) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX location_name_index IF NOT EXISTS FOR (l:Location) ON (l.name)'
2025-05-27 15:05:36,301 - neo4j_client - INFO - Executed: CREATE INDEX person_name_index IF NOT EXISTS FOR (p:Person) ON (p.name)
2025-05-27 15:05:36,301 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX person_name_index IF NOT EXISTS FOR (e:Person) ON (e.name)` has no effect.} {description: `RANGE INDEX person_name_unique FOR (e:Person) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX person_name_index IF NOT EXISTS FOR (p:Person) ON (p.name)'
2025-05-27 15:05:36,301 - __main__ - INFO - Fetching EPG data from 2025-05-27 to 2025-05-28
2025-05-27 15:05:36,301 - api_client - INFO - Fetching EPG data from https://supersport.com/apix/guide/v5.3/tvguide with params: {'countryCode': 'za', 'channelOnly': 'false', 'startDateTime': '2025-05-27', 'endDateTime': '2025-05-28', 'liveOnly': 'false'}
2025-05-27 15:05:37,466 - api_client - INFO - Successfully fetched 516 programs
2025-05-27 15:05:37,477 - __main__ - INFO - Fetched 516 programs
2025-05-27 15:05:37,477 - __main__ - INFO - Ingesting data into Neo4j...
2025-05-27 15:14:46,577 - neo4j_client - INFO - Ingestion completed. Stats: {'channels': 516, 'programs': 516, 'sports': 516, 'genres': 1032, 'packages': 516, 'teams': 400, 'tournaments': 185, 'venues': 263, 'locations': 263, 'people': 934, 'age_groups': 17, 'episodes': 70, 'relationships': 4956}
2025-05-27 15:14:46,578 - __main__ - INFO - Ingestion completed. Stats: {'channels': 516, 'programs': 516, 'sports': 516, 'genres': 1032, 'packages': 516, 'teams': 400, 'tournaments': 185, 'venues': 263, 'locations': 263, 'people': 934, 'age_groups': 17, 'episodes': 70, 'relationships': 4956}
2025-05-27 15:14:46,579 - __main__ - INFO - Total programs processed: 516
2025-05-27 15:14:46,579 - __main__ - INFO - Ingestion stats: {'channels': 516, 'programs': 516, 'sports': 516, 'genres': 1032, 'packages': 516, 'teams': 400, 'tournaments': 185, 'venues': 263, 'locations': 263, 'people': 934, 'age_groups': 17, 'episodes': 70, 'relationships': 4956}
2025-05-27 15:14:47,687 - __main__ - INFO - Final database statistics: {
  "total_channels": 29,
  "total_programs": 516,
  "total_sports": 29,
  "total_genres": 30,
  "total_packages": 1,
  "total_teams": 150,
  "total_tournaments": 124,
  "total_venues": 89,
  "total_locations": 76,
  "total_people": 297,
  "total_age_groups": 5,
  "total_episodes": 39,
  "total_relationships": 4588,
  "live_programs": 29,
  "programs_by_sport": [
    {
      "sport": "Football",
      "count": 182
    },
    {
      "sport": "Rugby",
      "count": 55
    },
    {
      "sport": "Tennis",
      "count": 36
    },
    {
      "sport": "Motorsport",
      "count": 33
    },
    {
      "sport": "Generic Sports",
      "count": 30
    },
    {
      "sport": "Cricket",
      "count": 29
    },
    {
      "sport": "Variety",
      "count": 26
    },
    {
      "sport": "MMA",
      "count": 20
    },
    {
      "sport": "Athletics",
      "count": 19
    },
    {
      "sport": "Golf",
      "count": 17
    }
  ],
  "channels_with_most_programs": [
    {
      "channel": "SS Premier League",
      "program_count": 54
    },
    {
      "channel": "SS Variety 3",
      "program_count": 35
    },
    {
      "channel": "SuperSport PSL",
      "program_count": 30
    },
    {
      "channel": "ESPN HD",
      "program_count": 29
    },
    {
      "channel": "SuperSport Blitz",
      "program_count": 28
    },
    {
      "channel": "SS Football",
      "program_count": 28
    },
    {
      "channel": "SS Rugby",
      "program_count": 28
    },
    {
      "channel": "SuperSport School HD",
      "program_count": 28
    },
    {
      "channel": "SS Variety 1 Nigeria",
      "program_count": 26
    },
    {
      "channel": "SS Cricket",
      "program_count": 25
    }
  ],
  "most_active_teams": [
    {
      "team": "Mamelodi Sundowns",
      "tournaments": 12
    },
    {
      "team": "of Excellence",
      "tournaments": 9
    },
    {
      "team": "Chelsea",
      "tournaments": 7
    },
    {
      "team": "Fiorentina",
      "tournaments": 6
    },
    {
      "team": "Kaizer Chiefs",
      "tournaments": 5
    },
    {
      "team": "West Ham United",
      "tournaments": 4
    },
    {
      "team": "Real Betis",
      "tournaments": 4
    },
    {
      "team": "Bath Rugby",
      "tournaments": 3
    },
    {
      "team": "Lyon Olympique",
      "tournaments": 3
    },
    {
      "team": "RS Berkane",
      "tournaments": 3
    }
  ],
  "popular_venues": [
    {
      "venue": "Roland Garros Stadium",
      "location": "Paris, France",
      "programs": 30
    },
    {
      "venue": "Principality Stadium",
      "location": "Cardiff, Wales",
      "programs": 12
    },
    {
      "venue": "Sawai Mansingh Stadium",
      "location": "Jaipur, India",
      "programs": 9
    },
    {
      "venue": "Silverstone Circuit",
      "location": "Silverstone, England",
      "programs": 9
    },
    {
      "venue": "Anfield",
      "location": "Liverpool, England",
      "programs": 8
    },
    {
      "venue": "Circuit de Monaco",
      "location": "Monte Carlo, Monaco",
      "programs": 8
    },
    {
      "venue": "UFC Apex",
      "location": "Las Vegas, USA",
      "programs": 6
    },
    {
      "venue": "Mbombela Stadium",
      "location": "Nelspruit, South Africa",
      "programs": 6
    },
    {
      "venue": "Moses Mabhida Stadium",
      "location": "Durban, South Africa",
      "programs": 5
    },
    {
      "venue": "Loftus Versfeld",
      "location": "Pretoria, South Africa",
      "programs": 5
    }
  ],
  "tournaments_by_programs": [
    {
      "tournament": "Gauteng Development League - U17: School of Excellence vs Mamelodi Sundowns",
      "teams": 2
    },
    {
      "tournament": "Gauteng Development League Blitz Highlights - U13: School of Excellence vs Mamelodi Sundowns",
      "teams": 2
    },
    {
      "tournament": "Gauteng Development League Blitz Highlights - U14: School of Excellence vs Mamelodi Sundowns",
      "teams": 2
    },
    {
      "tournament": "Gauteng Development League Blitz Highlights - U15: School of Excellence vs Mamelodi Sundowns",
      "teams": 2
    },
    {
      "tournament": "Gauteng Development League Blitz Highlights - U17: School of Excellence vs Mamelodi Sundowns",
      "teams": 2
    },
    {
      "tournament": "Gauteng Development League Blitz Highlights - U19: School of Excellence vs Mamelodi Sundowns",
      "teams": 2
    },
    {
      "tournament": "Gauteng Development League - U13: School of Excellence vs Mamelodi Sundowns",
      "teams": 2
    },
    {
      "tournament": "Gauteng Development League - U14: School of Excellence vs Mamelodi Sundowns",
      "teams": 2
    },
    {
      "tournament": "KZN Football League Blitz Highlights - U19 Coastal Final: Port Shepstone High School vs Glenwood High School",
      "teams": 2
    },
    {
      "tournament": "Gauteng Development League - U15: School of Excellence vs Mamelodi Sundowns",
      "teams": 2
    }
  ]
}
2025-05-27 15:14:47,691 - __main__ - INFO - EPG Data Ingestion completed successfully
2025-05-27 15:43:44,379 - __main__ - INFO - Starting EPG Data Ingestion
2025-05-27 15:43:44,380 - __main__ - INFO - Configuration validated successfully
2025-05-27 15:43:44,382 - __main__ - INFO - Testing API connection...
2025-05-27 15:43:46,169 - __main__ - INFO - API connection successful
2025-05-27 15:43:46,170 - __main__ - INFO - Testing Neo4j connection...
2025-05-27 15:43:46,696 - __main__ - INFO - Neo4j connection successful
2025-05-27 15:43:46,697 - __main__ - INFO - Setting up database constraints and indexes...
2025-05-27 15:43:46,744 - neo4j_client - INFO - Executed: CREATE CONSTRAINT channel_id_unique IF NOT EXISTS FOR (c:Channel) REQUIRE c.id IS UNIQUE
2025-05-27 15:43:46,745 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE CONSTRAINT channel_id_unique IF NOT EXISTS FOR (e:Channel) REQUIRE (e.id) IS UNIQUE` has no effect.} {description: `CONSTRAINT channel_id_unique FOR (e:Channel) REQUIRE (e.id) IS UNIQUE` already exists.} {position: None} for query: 'CREATE CONSTRAINT channel_id_unique IF NOT EXISTS FOR (c:Channel) REQUIRE c.id IS UNIQUE'
2025-05-27 15:43:46,798 - neo4j_client - INFO - Executed: CREATE CONSTRAINT sport_name_unique IF NOT EXISTS FOR (s:Sport) REQUIRE s.name IS UNIQUE
2025-05-27 15:43:46,799 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE CONSTRAINT sport_name_unique IF NOT EXISTS FOR (e:Sport) REQUIRE (e.name) IS UNIQUE` has no effect.} {description: `CONSTRAINT sport_name_unique FOR (e:Sport) REQUIRE (e.name) IS UNIQUE` already exists.} {position: None} for query: 'CREATE CONSTRAINT sport_name_unique IF NOT EXISTS FOR (s:Sport) REQUIRE s.name IS UNIQUE'
2025-05-27 15:43:46,847 - neo4j_client - INFO - Executed: CREATE CONSTRAINT genre_name_unique IF NOT EXISTS FOR (g:Genre) REQUIRE g.name IS UNIQUE
2025-05-27 15:43:46,849 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE CONSTRAINT genre_name_unique IF NOT EXISTS FOR (e:Genre) REQUIRE (e.name) IS UNIQUE` has no effect.} {description: `CONSTRAINT genre_name_unique FOR (e:Genre) REQUIRE (e.name) IS UNIQUE` already exists.} {position: None} for query: 'CREATE CONSTRAINT genre_name_unique IF NOT EXISTS FOR (g:Genre) REQUIRE g.name IS UNIQUE'
2025-05-27 15:43:46,900 - neo4j_client - INFO - Executed: CREATE CONSTRAINT package_name_unique IF NOT EXISTS FOR (p:Package) REQUIRE p.name IS UNIQUE
2025-05-27 15:43:46,901 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE CONSTRAINT package_name_unique IF NOT EXISTS FOR (e:Package) REQUIRE (e.name) IS UNIQUE` has no effect.} {description: `CONSTRAINT package_name_unique FOR (e:Package) REQUIRE (e.name) IS UNIQUE` already exists.} {position: None} for query: 'CREATE CONSTRAINT package_name_unique IF NOT EXISTS FOR (p:Package) REQUIRE p.name IS UNIQUE'
2025-05-27 15:43:46,945 - neo4j_client - INFO - Executed: CREATE CONSTRAINT team_name_unique IF NOT EXISTS FOR (t:Team) REQUIRE t.name IS UNIQUE
2025-05-27 15:43:46,947 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE CONSTRAINT team_name_unique IF NOT EXISTS FOR (e:Team) REQUIRE (e.name) IS UNIQUE` has no effect.} {description: `CONSTRAINT team_name_unique FOR (e:Team) REQUIRE (e.name) IS UNIQUE` already exists.} {position: None} for query: 'CREATE CONSTRAINT team_name_unique IF NOT EXISTS FOR (t:Team) REQUIRE t.name IS UNIQUE'
2025-05-27 15:43:47,000 - neo4j_client - INFO - Executed: CREATE CONSTRAINT tournament_name_unique IF NOT EXISTS FOR (t:Tournament) REQUIRE t.name IS UNIQUE
2025-05-27 15:43:47,008 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE CONSTRAINT tournament_name_unique IF NOT EXISTS FOR (e:Tournament) REQUIRE (e.name) IS UNIQUE` has no effect.} {description: `CONSTRAINT tournament_name_unique FOR (e:Tournament) REQUIRE (e.name) IS UNIQUE` already exists.} {position: None} for query: 'CREATE CONSTRAINT tournament_name_unique IF NOT EXISTS FOR (t:Tournament) REQUIRE t.name IS UNIQUE'
2025-05-27 15:43:47,064 - neo4j_client - INFO - Executed: CREATE CONSTRAINT venue_name_unique IF NOT EXISTS FOR (v:Venue) REQUIRE v.name IS UNIQUE
2025-05-27 15:43:47,072 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE CONSTRAINT venue_name_unique IF NOT EXISTS FOR (e:Venue) REQUIRE (e.name) IS UNIQUE` has no effect.} {description: `CONSTRAINT venue_name_unique FOR (e:Venue) REQUIRE (e.name) IS UNIQUE` already exists.} {position: None} for query: 'CREATE CONSTRAINT venue_name_unique IF NOT EXISTS FOR (v:Venue) REQUIRE v.name IS UNIQUE'
2025-05-27 15:43:47,119 - neo4j_client - INFO - Executed: CREATE CONSTRAINT location_name_unique IF NOT EXISTS FOR (l:Location) REQUIRE l.name IS UNIQUE
2025-05-27 15:43:47,120 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE CONSTRAINT location_name_unique IF NOT EXISTS FOR (e:Location) REQUIRE (e.name) IS UNIQUE` has no effect.} {description: `CONSTRAINT location_name_unique FOR (e:Location) REQUIRE (e.name) IS UNIQUE` already exists.} {position: None} for query: 'CREATE CONSTRAINT location_name_unique IF NOT EXISTS FOR (l:Location) REQUIRE l.name IS UNIQUE'
2025-05-27 15:43:47,172 - neo4j_client - INFO - Executed: CREATE CONSTRAINT person_name_unique IF NOT EXISTS FOR (p:Person) REQUIRE p.name IS UNIQUE
2025-05-27 15:43:47,173 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE CONSTRAINT person_name_unique IF NOT EXISTS FOR (e:Person) REQUIRE (e.name) IS UNIQUE` has no effect.} {description: `CONSTRAINT person_name_unique FOR (e:Person) REQUIRE (e.name) IS UNIQUE` already exists.} {position: None} for query: 'CREATE CONSTRAINT person_name_unique IF NOT EXISTS FOR (p:Person) REQUIRE p.name IS UNIQUE'
2025-05-27 15:43:47,218 - neo4j_client - INFO - Executed: CREATE CONSTRAINT age_group_name_unique IF NOT EXISTS FOR (a:AgeGroup) REQUIRE a.name IS UNIQUE
2025-05-27 15:43:47,219 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE CONSTRAINT age_group_name_unique IF NOT EXISTS FOR (e:AgeGroup) REQUIRE (e.name) IS UNIQUE` has no effect.} {description: `CONSTRAINT age_group_name_unique FOR (e:AgeGroup) REQUIRE (e.name) IS UNIQUE` already exists.} {position: None} for query: 'CREATE CONSTRAINT age_group_name_unique IF NOT EXISTS FOR (a:AgeGroup) REQUIRE a.name IS UNIQUE'
2025-05-27 15:43:47,270 - neo4j_client - INFO - Executed: CREATE INDEX channel_name_index IF NOT EXISTS FOR (c:Channel) ON (c.name)
2025-05-27 15:43:47,272 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX channel_name_index IF NOT EXISTS FOR (e:Channel) ON (e.name)` has no effect.} {description: `RANGE INDEX channel_name_index FOR (e:Channel) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX channel_name_index IF NOT EXISTS FOR (c:Channel) ON (c.name)'
2025-05-27 15:43:47,319 - neo4j_client - INFO - Executed: CREATE INDEX program_title_index IF NOT EXISTS FOR (p:Program) ON (p.title)
2025-05-27 15:43:47,321 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX program_title_index IF NOT EXISTS FOR (e:Program) ON (e.title)` has no effect.} {description: `RANGE INDEX program_title_index FOR (e:Program) ON (e.title)` already exists.} {position: None} for query: 'CREATE INDEX program_title_index IF NOT EXISTS FOR (p:Program) ON (p.title)'
2025-05-27 15:43:47,369 - neo4j_client - INFO - Executed: CREATE INDEX program_start_time_index IF NOT EXISTS FOR (p:Program) ON (p.start_time)
2025-05-27 15:43:47,371 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX program_start_time_index IF NOT EXISTS FOR (e:Program) ON (e.start_time)` has no effect.} {description: `RANGE INDEX program_start_time_index FOR (e:Program) ON (e.start_time)` already exists.} {position: None} for query: 'CREATE INDEX program_start_time_index IF NOT EXISTS FOR (p:Program) ON (p.start_time)'
2025-05-27 15:43:47,419 - neo4j_client - INFO - Executed: CREATE INDEX program_sport_index IF NOT EXISTS FOR (p:Program) ON (p.sport)
2025-05-27 15:43:47,420 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX program_sport_index IF NOT EXISTS FOR (e:Program) ON (e.sport)` has no effect.} {description: `RANGE INDEX program_sport_index FOR (e:Program) ON (e.sport)` already exists.} {position: None} for query: 'CREATE INDEX program_sport_index IF NOT EXISTS FOR (p:Program) ON (p.sport)'
2025-05-27 15:43:47,469 - neo4j_client - INFO - Executed: CREATE INDEX team_name_index IF NOT EXISTS FOR (t:Team) ON (t.name)
2025-05-27 15:43:47,470 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX team_name_index IF NOT EXISTS FOR (e:Team) ON (e.name)` has no effect.} {description: `RANGE INDEX team_name_unique FOR (e:Team) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX team_name_index IF NOT EXISTS FOR (t:Team) ON (t.name)'
2025-05-27 15:43:47,515 - neo4j_client - INFO - Executed: CREATE INDEX tournament_name_index IF NOT EXISTS FOR (t:Tournament) ON (t.name)
2025-05-27 15:43:47,517 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX tournament_name_index IF NOT EXISTS FOR (e:Tournament) ON (e.name)` has no effect.} {description: `RANGE INDEX tournament_name_unique FOR (e:Tournament) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX tournament_name_index IF NOT EXISTS FOR (t:Tournament) ON (t.name)'
2025-05-27 15:43:47,563 - neo4j_client - INFO - Executed: CREATE INDEX venue_name_index IF NOT EXISTS FOR (v:Venue) ON (v.name)
2025-05-27 15:43:47,565 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX venue_name_index IF NOT EXISTS FOR (e:Venue) ON (e.name)` has no effect.} {description: `RANGE INDEX venue_name_unique FOR (e:Venue) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX venue_name_index IF NOT EXISTS FOR (v:Venue) ON (v.name)'
2025-05-27 15:43:47,617 - neo4j_client - INFO - Executed: CREATE INDEX location_name_index IF NOT EXISTS FOR (l:Location) ON (l.name)
2025-05-27 15:43:47,619 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX location_name_index IF NOT EXISTS FOR (e:Location) ON (e.name)` has no effect.} {description: `RANGE INDEX location_name_unique FOR (e:Location) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX location_name_index IF NOT EXISTS FOR (l:Location) ON (l.name)'
2025-05-27 15:43:47,666 - neo4j_client - INFO - Executed: CREATE INDEX person_name_index IF NOT EXISTS FOR (p:Person) ON (p.name)
2025-05-27 15:43:47,670 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX person_name_index IF NOT EXISTS FOR (e:Person) ON (e.name)` has no effect.} {description: `RANGE INDEX person_name_unique FOR (e:Person) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX person_name_index IF NOT EXISTS FOR (p:Person) ON (p.name)'
2025-05-27 15:43:47,671 - __main__ - INFO - Fetching EPG data from 2025-05-27 to 2025-05-28
2025-05-27 15:43:47,672 - api_client - INFO - Fetching EPG data from https://supersport.com/apix/guide/v5.3/tvguide with params: {'countryCode': 'za', 'channelOnly': 'false', 'startDateTime': '2025-05-27', 'endDateTime': '2025-05-28', 'liveOnly': 'false'}
2025-05-27 15:43:48,917 - api_client - INFO - Successfully fetched 516 programs
2025-05-27 15:43:48,921 - __main__ - INFO - Fetched 516 programs
2025-05-27 15:43:48,922 - __main__ - INFO - Ingesting data into Neo4j...
2025-05-27 15:43:48,922 - neo4j_client - INFO - Starting ingestion of 516 programs
2025-05-27 15:43:50,650 - neo4j_client - INFO - Progress: 1/516 programs processed (0%) - Programs: 1, Relationships: 12
2025-05-27 15:44:23,400 - neo4j_client - INFO - Progress: 25/516 programs processed (4%) - Programs: 25, Relationships: 291
2025-05-27 15:44:24,347 - neo4j_client - INFO - Progress: 26/516 programs processed (5%) - Programs: 26, Relationships: 299
2025-05-27 15:44:40,078 - neo4j_client - INFO - Progress: 50/516 programs processed (9%) - Programs: 50, Relationships: 441
2025-05-27 15:44:41,099 - neo4j_client - INFO - Progress: 52/516 programs processed (10%) - Programs: 52, Relationships: 451
2025-05-27 15:45:04,767 - neo4j_client - INFO - Progress: 75/516 programs processed (14%) - Programs: 75, Relationships: 663
2025-05-27 15:45:07,575 - neo4j_client - INFO - Progress: 78/516 programs processed (15%) - Programs: 78, Relationships: 688
2025-05-27 15:45:31,580 - neo4j_client - INFO - Progress: 100/516 programs processed (19%) - Programs: 100, Relationships: 924
2025-05-27 15:45:36,518 - neo4j_client - INFO - Progress: 104/516 programs processed (20%) - Programs: 104, Relationships: 971
2025-05-27 15:45:59,214 - __main__ - ERROR - EPG Data Ingestion failed: Existing exports of data: object cannot be re-sized
Traceback (most recent call last):
  File "D:\Project\TATA_MCVR\Dev\EPG_DATA_INGESTION\neo4j_client.py", line 145, in ingest_epg_data
    enhanced_stats = self._create_enhanced_nodes(session, parsed_synopsis)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Project\TATA_MCVR\Dev\EPG_DATA_INGESTION\neo4j_client.py", line 290, in _create_enhanced_nodes
    self._create_venue_node(session, parsed_synopsis.venue, parsed_synopsis.location)
  File "D:\Project\TATA_MCVR\Dev\EPG_DATA_INGESTION\neo4j_client.py", line 340, in _create_venue_node
    session.run(query, {"name": venue_name, "location": location or ""})
  File "D:\Project\TATA_MCVR\Dev\EPG_DATA_INGESTION\.venv\Lib\site-packages\neo4j\_sync\work\session.py", line 328, in run
    self._auto_result._run(
  File "D:\Project\TATA_MCVR\Dev\EPG_DATA_INGESTION\.venv\Lib\site-packages\neo4j\_sync\work\result.py", line 236, in _run
    self._attach()
  File "D:\Project\TATA_MCVR\Dev\EPG_DATA_INGESTION\.venv\Lib\site-packages\neo4j\_sync\work\result.py", line 430, in _attach
    self._connection.fetch_message()
  File "D:\Project\TATA_MCVR\Dev\EPG_DATA_INGESTION\.venv\Lib\site-packages\neo4j\_sync\io\_common.py", line 184, in inner
    func(*args, **kwargs)
  File "D:\Project\TATA_MCVR\Dev\EPG_DATA_INGESTION\.venv\Lib\site-packages\neo4j\_sync\io\_bolt.py", line 861, in fetch_message
    tag, fields = self.inbox.pop(
                  ^^^^^^^^^^^^^^^
  File "D:\Project\TATA_MCVR\Dev\EPG_DATA_INGESTION\.venv\Lib\site-packages\neo4j\_sync\io\_common.py", line 77, in pop
    self._buffer_one_chunk()
  File "D:\Project\TATA_MCVR\Dev\EPG_DATA_INGESTION\.venv\Lib\site-packages\neo4j\_sync\io\_common.py", line 54, in _buffer_one_chunk
    receive_into_buffer(self._socket, self._buffer, 2)
  File "D:\Project\TATA_MCVR\Dev\EPG_DATA_INGESTION\.venv\Lib\site-packages\neo4j\_sync\io\_common.py", line 345, in receive_into_buffer
    n = sock.recv_into(
        ^^^^^^^^^^^^^^^
  File "D:\Project\TATA_MCVR\Dev\EPG_DATA_INGESTION\.venv\Lib\site-packages\neo4j\_async_compat\network\_bolt_socket.py", line 364, in recv_into
    return self._wait_for_io(self._socket.recv_into, buffer, nbytes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Project\TATA_MCVR\Dev\EPG_DATA_INGESTION\.venv\Lib\site-packages\neo4j\_async_compat\network\_bolt_socket.py", line 339, in _wait_for_io
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\ProgramData\anaconda3\Lib\ssl.py", line 1251, in recv_into
    return self.read(nbytes, buffer)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\ProgramData\anaconda3\Lib\ssl.py", line 1103, in read
    return self._sslobj.read(len, buffer)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Project\TATA_MCVR\Dev\EPG_DATA_INGESTION\.venv\Lib\site-packages\neo4j\_sync\work\session.py", line 222, in close
    self._connection.fetch_all()
  File "D:\Project\TATA_MCVR\Dev\EPG_DATA_INGESTION\.venv\Lib\site-packages\neo4j\_sync\io\_bolt.py", line 879, in fetch_all
    detail_delta, summary_delta = self.fetch_message()
                                  ^^^^^^^^^^^^^^^^^^^^
  File "D:\Project\TATA_MCVR\Dev\EPG_DATA_INGESTION\.venv\Lib\site-packages\neo4j\_sync\io\_bolt.py", line 861, in fetch_message
    tag, fields = self.inbox.pop(
                  ^^^^^^^^^^^^^^^
  File "D:\Project\TATA_MCVR\Dev\EPG_DATA_INGESTION\.venv\Lib\site-packages\neo4j\_sync\io\_common.py", line 77, in pop
    self._buffer_one_chunk()
  File "D:\Project\TATA_MCVR\Dev\EPG_DATA_INGESTION\.venv\Lib\site-packages\neo4j\_sync\io\_common.py", line 59, in _buffer_one_chunk
    receive_into_buffer(
  File "D:\Project\TATA_MCVR\Dev\EPG_DATA_INGESTION\.venv\Lib\site-packages\neo4j\_sync\io\_common.py", line 342, in receive_into_buffer
    buffer.data += bytearray(end - len(buffer.data))
BufferError: Existing exports of data: object cannot be re-sized

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Project\TATA_MCVR\Dev\EPG_DATA_INGESTION\main.py", line 80, in main
    stats = neo4j_client.ingest_epg_data(epg_response)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Project\TATA_MCVR\Dev\EPG_DATA_INGESTION\neo4j_client.py", line 114, in ingest_epg_data
    with self.driver.session(database=self.database) as session:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Project\TATA_MCVR\Dev\EPG_DATA_INGESTION\.venv\Lib\site-packages\neo4j\_sync\work\session.py", line 130, in __exit__
    self.close()
  File "D:\Project\TATA_MCVR\Dev\EPG_DATA_INGESTION\.venv\Lib\site-packages\neo4j\_sync\work\session.py", line 233, in close
    self._disconnect()
  File "D:\Project\TATA_MCVR\Dev\EPG_DATA_INGESTION\.venv\Lib\site-packages\neo4j\_sync\work\session.py", line 145, in _disconnect
    return super()._disconnect(sync=sync)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Project\TATA_MCVR\Dev\EPG_DATA_INGESTION\.venv\Lib\site-packages\neo4j\_sync\work\workspace.py", line 298, in _disconnect
    self._pool.release(self._connection)
  File "D:\Project\TATA_MCVR\Dev\EPG_DATA_INGESTION\.venv\Lib\site-packages\neo4j\_sync\io\_pool.py", line 481, in release
    connection.reset()
  File "D:\Project\TATA_MCVR\Dev\EPG_DATA_INGESTION\.venv\Lib\site-packages\neo4j\_sync\io\_bolt5.py", line 447, in reset
    self.fetch_all()
  File "D:\Project\TATA_MCVR\Dev\EPG_DATA_INGESTION\.venv\Lib\site-packages\neo4j\_sync\io\_bolt.py", line 879, in fetch_all
    detail_delta, summary_delta = self.fetch_message()
                                  ^^^^^^^^^^^^^^^^^^^^
  File "D:\Project\TATA_MCVR\Dev\EPG_DATA_INGESTION\.venv\Lib\site-packages\neo4j\_sync\io\_bolt.py", line 861, in fetch_message
    tag, fields = self.inbox.pop(
                  ^^^^^^^^^^^^^^^
  File "D:\Project\TATA_MCVR\Dev\EPG_DATA_INGESTION\.venv\Lib\site-packages\neo4j\_sync\io\_common.py", line 77, in pop
    self._buffer_one_chunk()
  File "D:\Project\TATA_MCVR\Dev\EPG_DATA_INGESTION\.venv\Lib\site-packages\neo4j\_sync\io\_common.py", line 59, in _buffer_one_chunk
    receive_into_buffer(
  File "D:\Project\TATA_MCVR\Dev\EPG_DATA_INGESTION\.venv\Lib\site-packages\neo4j\_sync\io\_common.py", line 342, in receive_into_buffer
    buffer.data += bytearray(end - len(buffer.data))
BufferError: Existing exports of data: object cannot be re-sized
2025-05-27 19:35:40,297 - __main__ - INFO - Starting EPG Data Ingestion
2025-05-27 19:35:40,304 - __main__ - INFO - Configuration validated successfully
2025-05-27 19:35:40,326 - __main__ - INFO - Testing API connection...
2025-05-27 19:35:42,140 - __main__ - INFO - API connection successful
2025-05-27 19:35:42,145 - __main__ - INFO - Testing Neo4j connection...
2025-05-27 19:35:43,286 - __main__ - INFO - Neo4j connection successful
2025-05-27 19:35:43,286 - __main__ - INFO - Setting up database constraints and indexes...
2025-05-27 19:35:43,381 - neo4j_client - INFO - Executed: CREATE CONSTRAINT channel_id_unique IF NOT EXISTS FOR (c:Channel) REQUIRE c.id IS UNIQUE
2025-05-27 19:35:43,471 - neo4j_client - INFO - Executed: CREATE CONSTRAINT sport_name_unique IF NOT EXISTS FOR (s:Sport) REQUIRE s.name IS UNIQUE
2025-05-27 19:35:43,564 - neo4j_client - INFO - Executed: CREATE CONSTRAINT genre_name_unique IF NOT EXISTS FOR (g:Genre) REQUIRE g.name IS UNIQUE
2025-05-27 19:35:43,684 - neo4j_client - INFO - Executed: CREATE CONSTRAINT package_name_unique IF NOT EXISTS FOR (p:Package) REQUIRE p.name IS UNIQUE
2025-05-27 19:35:43,772 - neo4j_client - INFO - Executed: CREATE CONSTRAINT team_name_unique IF NOT EXISTS FOR (t:Team) REQUIRE t.name IS UNIQUE
2025-05-27 19:35:43,862 - neo4j_client - INFO - Executed: CREATE CONSTRAINT tournament_name_unique IF NOT EXISTS FOR (t:Tournament) REQUIRE t.name IS UNIQUE
2025-05-27 19:35:43,949 - neo4j_client - INFO - Executed: CREATE CONSTRAINT venue_name_unique IF NOT EXISTS FOR (v:Venue) REQUIRE v.name IS UNIQUE
2025-05-27 19:35:44,041 - neo4j_client - INFO - Executed: CREATE CONSTRAINT location_name_unique IF NOT EXISTS FOR (l:Location) REQUIRE l.name IS UNIQUE
2025-05-27 19:35:44,118 - neo4j_client - INFO - Executed: CREATE CONSTRAINT person_name_unique IF NOT EXISTS FOR (p:Person) REQUIRE p.name IS UNIQUE
2025-05-27 19:35:44,201 - neo4j_client - INFO - Executed: CREATE CONSTRAINT age_group_name_unique IF NOT EXISTS FOR (a:AgeGroup) REQUIRE a.name IS UNIQUE
2025-05-27 19:35:44,254 - neo4j_client - INFO - Executed: CREATE INDEX channel_name_index IF NOT EXISTS FOR (c:Channel) ON (c.name)
2025-05-27 19:35:44,329 - neo4j_client - INFO - Executed: CREATE INDEX program_title_index IF NOT EXISTS FOR (p:Program) ON (p.title)
2025-05-27 19:35:44,399 - neo4j_client - INFO - Executed: CREATE INDEX program_start_time_index IF NOT EXISTS FOR (p:Program) ON (p.start_time)
2025-05-27 19:35:44,469 - neo4j_client - INFO - Executed: CREATE INDEX program_sport_index IF NOT EXISTS FOR (p:Program) ON (p.sport)
2025-05-27 19:35:44,547 - neo4j_client - INFO - Executed: CREATE INDEX team_name_index IF NOT EXISTS FOR (t:Team) ON (t.name)
2025-05-27 19:35:44,549 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX team_name_index IF NOT EXISTS FOR (e:Team) ON (e.name)` has no effect.} {description: `RANGE INDEX team_name_unique FOR (e:Team) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX team_name_index IF NOT EXISTS FOR (t:Team) ON (t.name)'
2025-05-27 19:35:44,595 - neo4j_client - INFO - Executed: CREATE INDEX tournament_name_index IF NOT EXISTS FOR (t:Tournament) ON (t.name)
2025-05-27 19:35:44,596 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX tournament_name_index IF NOT EXISTS FOR (e:Tournament) ON (e.name)` has no effect.} {description: `RANGE INDEX tournament_name_unique FOR (e:Tournament) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX tournament_name_index IF NOT EXISTS FOR (t:Tournament) ON (t.name)'
2025-05-27 19:35:44,696 - neo4j_client - INFO - Executed: CREATE INDEX venue_name_index IF NOT EXISTS FOR (v:Venue) ON (v.name)
2025-05-27 19:35:44,698 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX venue_name_index IF NOT EXISTS FOR (e:Venue) ON (e.name)` has no effect.} {description: `RANGE INDEX venue_name_unique FOR (e:Venue) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX venue_name_index IF NOT EXISTS FOR (v:Venue) ON (v.name)'
2025-05-27 19:35:44,764 - neo4j_client - INFO - Executed: CREATE INDEX location_name_index IF NOT EXISTS FOR (l:Location) ON (l.name)
2025-05-27 19:35:44,767 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX location_name_index IF NOT EXISTS FOR (e:Location) ON (e.name)` has no effect.} {description: `RANGE INDEX location_name_unique FOR (e:Location) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX location_name_index IF NOT EXISTS FOR (l:Location) ON (l.name)'
2025-05-27 19:35:44,809 - neo4j_client - INFO - Executed: CREATE INDEX person_name_index IF NOT EXISTS FOR (p:Person) ON (p.name)
2025-05-27 19:35:44,809 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX person_name_index IF NOT EXISTS FOR (e:Person) ON (e.name)` has no effect.} {description: `RANGE INDEX person_name_unique FOR (e:Person) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX person_name_index IF NOT EXISTS FOR (p:Person) ON (p.name)'
2025-05-27 19:35:44,809 - __main__ - INFO - Fetching EPG data from 2025-05-27 to 2025-05-28
2025-05-27 19:35:44,809 - api_client - INFO - Fetching EPG data from https://supersport.com/apix/guide/v5.3/tvguide with params: {'countryCode': 'za', 'channelOnly': 'false', 'startDateTime': '2025-05-27', 'endDateTime': '2025-05-28', 'liveOnly': 'false'}
2025-05-27 19:35:45,954 - api_client - INFO - Successfully fetched 518 programs
2025-05-27 19:35:45,962 - __main__ - INFO - Fetched 518 programs
2025-05-27 19:35:45,962 - __main__ - INFO - Ingesting data into Neo4j...
2025-05-27 19:35:45,962 - neo4j_client - INFO - Starting ingestion of 518 programs
2025-05-27 19:35:47,913 - neo4j_client - INFO - Progress: 1/518 programs processed (0%) - Programs: 1, Relationships: 12
2025-05-27 19:36:19,886 - neo4j_client - INFO - Progress: 25/518 programs processed (4%) - Programs: 25, Relationships: 291
2025-05-27 19:36:20,735 - neo4j_client - INFO - Progress: 26/518 programs processed (5%) - Programs: 26, Relationships: 299
2025-05-27 19:36:35,196 - neo4j_client - INFO - Progress: 50/518 programs processed (9%) - Programs: 50, Relationships: 441
2025-05-27 19:36:36,134 - neo4j_client - INFO - Progress: 52/518 programs processed (10%) - Programs: 52, Relationships: 451
2025-05-27 19:36:57,451 - neo4j_client - INFO - Progress: 75/518 programs processed (14%) - Programs: 75, Relationships: 663
2025-05-27 19:36:59,983 - neo4j_client - INFO - Progress: 78/518 programs processed (15%) - Programs: 78, Relationships: 689
2025-05-27 19:37:22,719 - neo4j_client - INFO - Progress: 100/518 programs processed (19%) - Programs: 100, Relationships: 926
2025-05-27 19:37:26,465 - neo4j_client - INFO - Progress: 104/518 programs processed (20%) - Programs: 104, Relationships: 966
2025-05-27 19:37:53,597 - neo4j_client - INFO - Progress: 125/518 programs processed (24%) - Programs: 125, Relationships: 1247
2025-05-27 19:37:59,867 - neo4j_client - INFO - Progress: 130/518 programs processed (25%) - Programs: 130, Relationships: 1314
2025-05-27 19:38:23,281 - neo4j_client - INFO - Progress: 150/518 programs processed (28%) - Programs: 150, Relationships: 1562
2025-05-27 19:38:29,319 - neo4j_client - INFO - Progress: 156/518 programs processed (30%) - Programs: 156, Relationships: 1627
2025-05-27 19:38:46,359 - neo4j_client - INFO - Progress: 175/518 programs processed (33%) - Programs: 175, Relationships: 1807
2025-05-27 19:38:53,333 - neo4j_client - INFO - Progress: 182/518 programs processed (35%) - Programs: 182, Relationships: 1881
2025-05-27 19:39:11,685 - neo4j_client - INFO - Progress: 200/518 programs processed (38%) - Programs: 200, Relationships: 2072
2025-05-27 19:39:19,089 - neo4j_client - INFO - Progress: 208/518 programs processed (40%) - Programs: 208, Relationships: 2147
2025-05-27 19:39:32,569 - neo4j_client - INFO - Progress: 225/518 programs processed (43%) - Programs: 225, Relationships: 2288
2025-05-27 19:39:39,813 - neo4j_client - INFO - Progress: 234/518 programs processed (45%) - Programs: 234, Relationships: 2359
2025-05-27 19:39:53,503 - neo4j_client - INFO - Progress: 250/518 programs processed (48%) - Programs: 250, Relationships: 2499
2025-05-27 19:40:01,596 - neo4j_client - INFO - Progress: 259/518 programs processed (50%) - Programs: 259, Relationships: 2581
2025-05-27 19:40:15,838 - neo4j_client - INFO - Progress: 275/518 programs processed (53%) - Programs: 275, Relationships: 2724
2025-05-27 19:40:26,200 - neo4j_client - INFO - Progress: 285/518 programs processed (55%) - Programs: 285, Relationships: 2830
2025-05-27 19:40:41,274 - neo4j_client - INFO - Progress: 300/518 programs processed (57%) - Programs: 300, Relationships: 2977
2025-05-27 19:40:56,434 - neo4j_client - INFO - Progress: 311/518 programs processed (60%) - Programs: 311, Relationships: 3123
2025-05-27 19:41:13,514 - neo4j_client - INFO - Progress: 325/518 programs processed (62%) - Programs: 325, Relationships: 3303
2025-05-27 19:41:26,189 - neo4j_client - INFO - Progress: 337/518 programs processed (65%) - Programs: 337, Relationships: 3430
2025-05-27 19:41:39,153 - neo4j_client - INFO - Progress: 350/518 programs processed (67%) - Programs: 350, Relationships: 3565
2025-05-27 19:41:55,831 - neo4j_client - INFO - Progress: 363/518 programs processed (70%) - Programs: 363, Relationships: 3739
2025-05-27 19:42:08,927 - neo4j_client - INFO - Progress: 375/518 programs processed (72%) - Programs: 375, Relationships: 3873
2025-05-27 19:42:21,897 - neo4j_client - INFO - Progress: 389/518 programs processed (75%) - Programs: 389, Relationships: 4008
2025-05-27 19:42:31,923 - neo4j_client - INFO - Progress: 400/518 programs processed (77%) - Programs: 400, Relationships: 4112
2025-05-27 19:42:43,836 - neo4j_client - INFO - Progress: 415/518 programs processed (80%) - Programs: 415, Relationships: 4229
2025-05-27 19:42:50,732 - neo4j_client - INFO - Progress: 425/518 programs processed (82%) - Programs: 425, Relationships: 4299
2025-05-27 19:43:01,127 - neo4j_client - INFO - Progress: 441/518 programs processed (85%) - Programs: 441, Relationships: 4407
2025-05-27 19:43:06,765 - neo4j_client - INFO - Progress: 450/518 programs processed (86%) - Programs: 450, Relationships: 4466
2025-05-27 19:43:16,968 - neo4j_client - INFO - Progress: 467/518 programs processed (90%) - Programs: 467, Relationships: 4573
2025-05-27 19:43:22,952 - neo4j_client - INFO - Progress: 475/518 programs processed (91%) - Programs: 475, Relationships: 4635
2025-05-27 19:43:38,465 - neo4j_client - INFO - Progress: 493/518 programs processed (95%) - Programs: 493, Relationships: 4799
2025-05-27 19:43:45,433 - neo4j_client - INFO - Progress: 500/518 programs processed (96%) - Programs: 500, Relationships: 4874
2025-05-27 19:43:55,314 - neo4j_client - INFO - Progress: 518/518 programs processed (100%) - Programs: 518, Relationships: 4976
2025-05-27 19:43:55,331 - neo4j_client - INFO - Final stats: {'channels': 518, 'programs': 518, 'sports': 518, 'genres': 1036, 'packages': 518, 'teams': 400, 'tournaments': 185, 'venues': 265, 'locations': 265, 'people': 940, 'age_groups': 17, 'episodes': 70, 'relationships': 4976}
2025-05-27 19:43:55,332 - __main__ - INFO - Ingestion completed. Stats: {'channels': 518, 'programs': 518, 'sports': 518, 'genres': 1036, 'packages': 518, 'teams': 400, 'tournaments': 185, 'venues': 265, 'locations': 265, 'people': 940, 'age_groups': 17, 'episodes': 70, 'relationships': 4976}
2025-05-27 19:43:55,332 - __main__ - INFO - Total programs processed: 518
2025-05-27 19:43:55,333 - __main__ - INFO - Ingestion stats: {'channels': 518, 'programs': 518, 'sports': 518, 'genres': 1036, 'packages': 518, 'teams': 400, 'tournaments': 185, 'venues': 265, 'locations': 265, 'people': 940, 'age_groups': 17, 'episodes': 70, 'relationships': 4976}
2025-05-27 19:43:56,356 - __main__ - INFO - Final database statistics: {
  "total_channels": 29,
  "total_programs": 518,
  "total_sports": 29,
  "total_genres": 30,
  "total_packages": 1,
  "total_teams": 150,
  "total_tournaments": 124,
  "total_venues": 89,
  "total_locations": 76,
  "total_people": 299,
  "total_age_groups": 5,
  "total_episodes": 39,
  "total_relationships": 4606,
  "live_programs": 31,
  "programs_by_sport": [
    {
      "sport": "Football",
      "count": 182
    },
    {
      "sport": "Rugby",
      "count": 55
    },
    {
      "sport": "Tennis",
      "count": 38
    },
    {
      "sport": "Motorsport",
      "count": 33
    },
    {
      "sport": "Generic Sports",
      "count": 30
    },
    {
      "sport": "Cricket",
      "count": 29
    },
    {
      "sport": "Variety",
      "count": 26
    },
    {
      "sport": "MMA",
      "count": 20
    },
    {
      "sport": "Athletics",
      "count": 19
    },
    {
      "sport": "Golf",
      "count": 17
    }
  ],
  "channels_with_most_programs": [
    {
      "channel": "SS Premier League",
      "program_count": 54
    },
    {
      "channel": "SS Variety 3",
      "program_count": 35
    },
    {
      "channel": "SuperSport PSL",
      "program_count": 30
    },
    {
      "channel": "ESPN HD",
      "program_count": 29
    },
    {
      "channel": "SuperSport Blitz",
      "program_count": 28
    },
    {
      "channel": "SS Football",
      "program_count": 28
    },
    {
      "channel": "SS Rugby",
      "program_count": 28
    },
    {
      "channel": "SuperSport School HD",
      "program_count": 28
    },
    {
      "channel": "SS Variety 1 Nigeria",
      "program_count": 26
    },
    {
      "channel": "SS Cricket",
      "program_count": 25
    }
  ],
  "most_active_teams": [
    {
      "team": "Mamelodi Sundowns",
      "tournaments": 12
    },
    {
      "team": "of Excellence",
      "tournaments": 9
    },
    {
      "team": "Chelsea",
      "tournaments": 7
    },
    {
      "team": "Fiorentina",
      "tournaments": 6
    },
    {
      "team": "Kaizer Chiefs",
      "tournaments": 5
    },
    {
      "team": "West Ham United",
      "tournaments": 4
    },
    {
      "team": "Real Betis",
      "tournaments": 4
    },
    {
      "team": "Bath Rugby",
      "tournaments": 3
    },
    {
      "team": "Lyon Olympique",
      "tournaments": 3
    },
    {
      "team": "RS Berkane",
      "tournaments": 3
    }
  ],
  "popular_venues": [
    {
      "venue": "Roland Garros Stadium",
      "location": "Paris, France",
      "programs": 32
    },
    {
      "venue": "Principality Stadium",
      "location": "Cardiff, Wales",
      "programs": 12
    },
    {
      "venue": "Sawai Mansingh Stadium",
      "location": "Jaipur, India",
      "programs": 9
    },
    {
      "venue": "Silverstone Circuit",
      "location": "Silverstone, England",
      "programs": 9
    },
    {
      "venue": "Anfield",
      "location": "Liverpool, England",
      "programs": 8
    },
    {
      "venue": "Circuit de Monaco",
      "location": "Monte Carlo, Monaco",
      "programs": 8
    },
    {
      "venue": "UFC Apex",
      "location": "Las Vegas, USA",
      "programs": 6
    },
    {
      "venue": "Mbombela Stadium",
      "location": "Nelspruit, South Africa",
      "programs": 6
    },
    {
      "venue": "Moses Mabhida Stadium",
      "location": "Durban, South Africa",
      "programs": 5
    },
    {
      "venue": "Loftus Versfeld",
      "location": "Pretoria, South Africa",
      "programs": 5
    }
  ],
  "tournaments_by_programs": [
    {
      "tournament": "Gauteng Development League - U17: School of Excellence vs Mamelodi Sundowns",
      "teams": 2
    },
    {
      "tournament": "Gauteng Development League Blitz Highlights - U13: School of Excellence vs Mamelodi Sundowns",
      "teams": 2
    },
    {
      "tournament": "Gauteng Development League Blitz Highlights - U14: School of Excellence vs Mamelodi Sundowns",
      "teams": 2
    },
    {
      "tournament": "Gauteng Development League Blitz Highlights - U15: School of Excellence vs Mamelodi Sundowns",
      "teams": 2
    },
    {
      "tournament": "Gauteng Development League Blitz Highlights - U17: School of Excellence vs Mamelodi Sundowns",
      "teams": 2
    },
    {
      "tournament": "Gauteng Development League Blitz Highlights - U19: School of Excellence vs Mamelodi Sundowns",
      "teams": 2
    },
    {
      "tournament": "Gauteng Development League - U13: School of Excellence vs Mamelodi Sundowns",
      "teams": 2
    },
    {
      "tournament": "Gauteng Development League - U14: School of Excellence vs Mamelodi Sundowns",
      "teams": 2
    },
    {
      "tournament": "KZN Football League Blitz Highlights - U19 Coastal Final: Port Shepstone High School vs Glenwood High School",
      "teams": 2
    },
    {
      "tournament": "Gauteng Development League - U15: School of Excellence vs Mamelodi Sundowns",
      "teams": 2
    }
  ]
}
2025-05-27 19:43:56,362 - __main__ - INFO - EPG Data Ingestion completed successfully
2025-06-17 01:11:24,615 - __main__ - INFO - Starting EPG Data Ingestion
2025-06-17 01:11:24,615 - __main__ - INFO - Configuration validated successfully
2025-06-17 01:11:24,615 - __main__ - INFO - Testing API connection...
2025-06-17 01:11:25,596 - __main__ - INFO - API connection successful
2025-06-17 01:11:25,596 - __main__ - INFO - Testing Neo4j connection...
2025-06-17 01:11:26,702 - __main__ - INFO - Neo4j connection successful
2025-06-17 01:11:26,702 - __main__ - INFO - Setting up database constraints and indexes...
2025-06-17 01:11:26,860 - neo4j_client - INFO - Executed: CREATE CONSTRAINT program_id_unique IF NOT EXISTS FOR (p:Program) REQUIRE p.id IS UNIQUE
2025-06-17 01:11:26,952 - neo4j_client - INFO - Executed: CREATE CONSTRAINT channel_id_unique IF NOT EXISTS FOR (c:Channel) REQUIRE c.id IS UNIQUE
2025-06-17 01:11:27,027 - neo4j_client - INFO - Executed: CREATE INDEX program_title_index IF NOT EXISTS FOR (p:Program) ON (p.title)
2025-06-17 01:11:27,099 - neo4j_client - INFO - Executed: CREATE INDEX program_start_time_index IF NOT EXISTS FOR (p:Program) ON (p.start_time)
2025-06-17 01:11:27,177 - neo4j_client - INFO - Executed: CREATE INDEX program_end_time_index IF NOT EXISTS FOR (p:Program) ON (p.end_time)
2025-06-17 01:11:27,247 - neo4j_client - INFO - Executed: CREATE INDEX channel_name_index IF NOT EXISTS FOR (c:Channel) ON (c.name)
2025-06-17 01:11:27,331 - neo4j_client - INFO - Executed: CREATE INDEX channel_number_index IF NOT EXISTS FOR (c:Channel) ON (c.channel_number)
2025-06-17 01:11:27,353 - __main__ - INFO - Fetching EPG data from 2025-06-16 to 2025-06-17
2025-06-17 01:11:27,355 - api_client - INFO - Fetching EPG data from https://supersport.com/apix/guide/v5.3/tvguide with params: {'countryCode': 'za', 'channelOnly': 'false', 'startDateTime': '2025-06-16', 'endDateTime': '2025-06-17', 'liveOnly': 'false'}
2025-06-17 01:11:27,992 - api_client - INFO - Successfully fetched 507 programs
2025-06-17 01:11:27,992 - __main__ - INFO - Fetched 507 programs
2025-06-17 01:11:28,009 - __main__ - INFO - Ingesting data into Neo4j...
2025-06-17 01:11:28,009 - neo4j_client - INFO - Starting simplified ingestion of 507 programs
2025-06-17 01:11:29,330 - neo4j_client - INFO - Progress: 1/507 programs processed (0%) - Nodes: 11, Channels: 1, Relationships: 2
2025-06-17 01:11:48,177 - neo4j_client - INFO - Progress: 25/507 programs processed (4%) - Nodes: 281, Channels: 25, Relationships: 50
2025-06-17 01:11:48,887 - neo4j_client - INFO - Progress: 26/507 programs processed (5%) - Nodes: 293, Channels: 26, Relationships: 52
2025-06-17 01:12:05,953 - neo4j_client - INFO - Progress: 50/507 programs processed (9%) - Nodes: 563, Channels: 50, Relationships: 100
2025-06-17 01:12:06,638 - neo4j_client - INFO - Progress: 51/507 programs processed (10%) - Nodes: 574, Channels: 51, Relationships: 102
2025-06-17 01:12:23,473 - neo4j_client - INFO - Progress: 75/507 programs processed (14%) - Nodes: 848, Channels: 75, Relationships: 150
2025-06-17 01:12:24,770 - neo4j_client - INFO - Progress: 77/507 programs processed (15%) - Nodes: 870, Channels: 77, Relationships: 154
2025-06-17 01:12:40,221 - neo4j_client - INFO - Progress: 100/507 programs processed (19%) - Nodes: 1123, Channels: 100, Relationships: 200
2025-06-17 01:12:41,586 - neo4j_client - INFO - Progress: 102/507 programs processed (20%) - Nodes: 1146, Channels: 102, Relationships: 204
2025-06-17 01:12:56,642 - neo4j_client - INFO - Progress: 125/507 programs processed (24%) - Nodes: 1399, Channels: 125, Relationships: 250
2025-06-17 01:12:58,112 - neo4j_client - INFO - Progress: 127/507 programs processed (25%) - Nodes: 1422, Channels: 127, Relationships: 254
2025-06-17 01:13:14,015 - neo4j_client - INFO - Progress: 150/507 programs processed (29%) - Nodes: 1686, Channels: 150, Relationships: 300
2025-06-17 01:13:15,983 - neo4j_client - INFO - Progress: 153/507 programs processed (30%) - Nodes: 1719, Channels: 153, Relationships: 306
2025-06-17 01:13:30,821 - neo4j_client - INFO - Progress: 175/507 programs processed (34%) - Nodes: 1961, Channels: 175, Relationships: 350
2025-06-17 01:13:33,043 - neo4j_client - INFO - Progress: 178/507 programs processed (35%) - Nodes: 1995, Channels: 178, Relationships: 356
2025-06-17 01:13:47,775 - neo4j_client - INFO - Progress: 200/507 programs processed (39%) - Nodes: 2238, Channels: 200, Relationships: 400
2025-06-17 01:13:49,747 - neo4j_client - INFO - Progress: 203/507 programs processed (40%) - Nodes: 2271, Channels: 203, Relationships: 406
2025-06-17 01:14:04,728 - neo4j_client - INFO - Progress: 225/507 programs processed (44%) - Nodes: 2514, Channels: 225, Relationships: 450
2025-06-17 01:14:07,527 - neo4j_client - INFO - Progress: 229/507 programs processed (45%) - Nodes: 2558, Channels: 229, Relationships: 458
2025-06-17 01:14:22,119 - neo4j_client - INFO - Progress: 250/507 programs processed (49%) - Nodes: 2790, Channels: 250, Relationships: 500
2025-06-17 01:14:24,784 - neo4j_client - INFO - Progress: 254/507 programs processed (50%) - Nodes: 2835, Channels: 254, Relationships: 508
2025-06-17 01:14:39,333 - neo4j_client - INFO - Progress: 275/507 programs processed (54%) - Nodes: 3069, Channels: 275, Relationships: 550
2025-06-17 01:14:42,033 - neo4j_client - INFO - Progress: 279/507 programs processed (55%) - Nodes: 3113, Channels: 279, Relationships: 558
2025-06-17 01:14:55,913 - neo4j_client - INFO - Progress: 300/507 programs processed (59%) - Nodes: 3344, Channels: 300, Relationships: 600
2025-06-17 01:14:59,225 - neo4j_client - INFO - Progress: 305/507 programs processed (60%) - Nodes: 3399, Channels: 305, Relationships: 610
2025-06-17 01:15:14,062 - neo4j_client - INFO - Progress: 325/507 programs processed (64%) - Nodes: 3620, Channels: 325, Relationships: 650
2025-06-17 01:15:17,327 - neo4j_client - INFO - Progress: 330/507 programs processed (65%) - Nodes: 3675, Channels: 330, Relationships: 660
2025-06-17 01:15:30,339 - neo4j_client - INFO - Progress: 350/507 programs processed (69%) - Nodes: 3895, Channels: 350, Relationships: 700
2025-06-17 01:15:33,880 - neo4j_client - INFO - Progress: 355/507 programs processed (70%) - Nodes: 3950, Channels: 355, Relationships: 710
2025-06-17 01:15:47,314 - neo4j_client - INFO - Progress: 375/507 programs processed (73%) - Nodes: 4171, Channels: 375, Relationships: 750
2025-06-17 01:15:51,447 - neo4j_client - INFO - Progress: 381/507 programs processed (75%) - Nodes: 4238, Channels: 381, Relationships: 762
2025-06-17 01:16:03,777 - neo4j_client - INFO - Progress: 400/507 programs processed (78%) - Nodes: 4447, Channels: 400, Relationships: 800
2025-06-17 01:16:07,980 - neo4j_client - INFO - Progress: 406/507 programs processed (80%) - Nodes: 4513, Channels: 406, Relationships: 812
2025-06-17 01:16:20,763 - neo4j_client - INFO - Progress: 425/507 programs processed (83%) - Nodes: 4726, Channels: 425, Relationships: 850
2025-06-17 01:16:24,652 - neo4j_client - INFO - Progress: 431/507 programs processed (85%) - Nodes: 4793, Channels: 431, Relationships: 862
2025-06-17 01:16:37,085 - neo4j_client - INFO - Progress: 450/507 programs processed (88%) - Nodes: 5002, Channels: 450, Relationships: 900
2025-06-17 01:16:41,649 - neo4j_client - INFO - Progress: 457/507 programs processed (90%) - Nodes: 5080, Channels: 457, Relationships: 914
2025-06-17 01:16:53,055 - neo4j_client - INFO - Progress: 475/507 programs processed (93%) - Nodes: 5279, Channels: 475, Relationships: 950
2025-06-17 01:16:57,909 - neo4j_client - INFO - Progress: 482/507 programs processed (95%) - Nodes: 5356, Channels: 482, Relationships: 964
2025-06-17 01:17:09,377 - neo4j_client - INFO - Progress: 500/507 programs processed (98%) - Nodes: 5555, Channels: 500, Relationships: 1000
2025-06-17 01:17:13,665 - neo4j_client - INFO - Progress: 507/507 programs processed (100%) - Nodes: 5632, Channels: 507, Relationships: 1014
2025-06-17 01:17:13,679 - neo4j_client - INFO - Final stats: {'nodes_created': 5632, 'programs_processed': 507, 'channels_created': 507, 'relationships_created': 1014}
2025-06-17 01:17:13,680 - __main__ - INFO - Ingestion completed. Stats: {'nodes_created': 5632, 'programs_processed': 507, 'channels_created': 507, 'relationships_created': 1014}
2025-06-17 01:17:13,680 - __main__ - INFO - Total programs processed: 507
2025-06-17 01:17:13,681 - __main__ - INFO - Ingestion stats: {'nodes_created': 5632, 'programs_processed': 507, 'channels_created': 507, 'relationships_created': 1014}
2025-06-17 01:17:14,363 - __main__ - INFO - Final database statistics: {
  "total_nodes": 1756,
  "total_programs": 507,
  "total_channels": 27,
  "total_relationships": 1014,
  "broadcasts_relationships": 507,
  "aired_on_relationships": 507,
  "node_labels": [
    {
      "labels": [
        "Program",
        "Channel",
        "Sport",
        "End",
        "Start",
        "Title",
        "Name",
        "Rating",
        "Synopsis",
        "Thumbnailuri",
        "Package",
        "Islive"
      ]
    }
  ],
  "sample_nodes": [
    {
      "label": "Program",
      "count": 507
    },
    {
      "label": "Synopsis",
      "count": 281
    },
    {
      "label": "Thumbnailuri",
      "count": 240
    },
    {
      "label": "Title",
      "count": 234
    },
    {
      "label": "Start",
      "count": 202
    },
    {
      "label": "End",
      "count": 197
    },
    {
      "label": "Sport",
      "count": 35
    },
    {
      "label": "Name",
      "count": 27
    },
    {
      "label": "Channel",
      "count": 27
    },
    {
      "label": "Rating",
      "count": 3
    }
  ],
  "channel_program_counts": [
    {
      "channel_name": "Community Services Network",
      "channel_number": 0,
      "program_count": 36
    },
    {
      "channel_name": "SS Premier League",
      "channel_number": 0,
      "program_count": 34
    },
    {
      "channel_name": "SS WWE",
      "channel_number": 0,
      "program_count": 33
    },
    {
      "channel_name": "SS Variety 3",
      "channel_number": 0,
      "program_count": 32
    },
    {
      "channel_name": "SS Rugby",
      "channel_number": 0,
      "program_count": 31
    },
    {
      "channel_name": "SS Motorsport",
      "channel_number": 0,
      "program_count": 30
    },
    {
      "channel_name": "SuperSport PSL",
      "channel_number": 0,
      "program_count": 26
    },
    {
      "channel_name": "SuperSport School HD",
      "channel_number": 0,
      "program_count": 24
    },
    {
      "channel_name": "SS Cricket",
      "channel_number": 0,
      "program_count": 24
    },
    {
      "channel_name": "SS Football",
      "channel_number": 0,
      "program_count": 23
    }
  ]
}
2025-06-17 01:17:14,363 - __main__ - INFO - EPG Data Ingestion completed successfully
2025-06-17 01:37:29,985 - __main__ - INFO - Starting EPG Data Ingestion
2025-06-17 01:37:29,985 - __main__ - INFO - Configuration validated successfully
2025-06-17 01:37:30,001 - __main__ - INFO - Testing API connection...
2025-06-17 01:37:31,118 - __main__ - INFO - API connection successful
2025-06-17 01:37:31,118 - __main__ - INFO - Testing Neo4j connection...
2025-06-17 01:37:32,300 - __main__ - INFO - Neo4j connection successful
2025-06-17 01:37:32,300 - __main__ - INFO - Setting up database constraints and indexes...
2025-06-17 01:37:32,370 - neo4j_client - INFO - Executed: CREATE CONSTRAINT program_id_unique IF NOT EXISTS FOR (p:Program) REQUIRE p.id IS UNIQUE
2025-06-17 01:37:32,428 - neo4j_client - INFO - Executed: CREATE CONSTRAINT channel_id_unique IF NOT EXISTS FOR (c:Channel) REQUIRE c.id IS UNIQUE
2025-06-17 01:37:32,469 - neo4j_client - INFO - Executed: CREATE INDEX program_title_index IF NOT EXISTS FOR (p:Program) ON (p.title)
2025-06-17 01:37:32,536 - neo4j_client - INFO - Executed: CREATE INDEX program_start_time_index IF NOT EXISTS FOR (p:Program) ON (p.start_time)
2025-06-17 01:37:32,636 - neo4j_client - INFO - Executed: CREATE INDEX program_end_time_index IF NOT EXISTS FOR (p:Program) ON (p.end_time)
2025-06-17 01:37:32,688 - neo4j_client - INFO - Executed: CREATE INDEX channel_name_index IF NOT EXISTS FOR (c:Channel) ON (c.name)
2025-06-17 01:37:32,736 - neo4j_client - INFO - Executed: CREATE INDEX channel_number_index IF NOT EXISTS FOR (c:Channel) ON (c.channel_number)
2025-06-17 01:37:32,754 - __main__ - INFO - Fetching EPG data from 2025-06-16 to 2025-06-17
2025-06-17 01:37:32,754 - api_client - INFO - Fetching EPG data from https://supersport.com/apix/guide/v5.3/tvguide with params: {'countryCode': 'za', 'channelOnly': 'false', 'startDateTime': '2025-06-16', 'endDateTime': '2025-06-17', 'liveOnly': 'false'}
2025-06-17 01:37:34,205 - api_client - INFO - Successfully fetched 507 programs
2025-06-17 01:37:34,221 - __main__ - INFO - Fetched 507 programs
2025-06-17 01:37:34,226 - __main__ - INFO - Ingesting data into Neo4j...
2025-06-17 01:37:34,227 - neo4j_client - INFO - Starting simplified ingestion of 507 programs
2025-06-17 01:37:35,287 - neo4j_client - INFO - Progress: 1/507 programs processed (0%) - Nodes: 13, Channels: 1, Relationships: 2
2025-06-17 01:37:53,472 - neo4j_client - INFO - Progress: 25/507 programs processed (4%) - Nodes: 331, Channels: 25, Relationships: 50
2025-06-17 01:37:54,197 - neo4j_client - INFO - Progress: 26/507 programs processed (5%) - Nodes: 345, Channels: 26, Relationships: 52
2025-06-17 01:38:10,905 - neo4j_client - INFO - Progress: 50/507 programs processed (9%) - Nodes: 663, Channels: 50, Relationships: 100
2025-06-17 01:38:11,587 - neo4j_client - INFO - Progress: 51/507 programs processed (10%) - Nodes: 676, Channels: 51, Relationships: 102
2025-06-17 01:38:28,024 - neo4j_client - INFO - Progress: 75/507 programs processed (14%) - Nodes: 998, Channels: 75, Relationships: 150
2025-06-17 01:38:29,346 - neo4j_client - INFO - Progress: 77/507 programs processed (15%) - Nodes: 1024, Channels: 77, Relationships: 154
2025-06-17 01:38:44,542 - neo4j_client - INFO - Progress: 100/507 programs processed (19%) - Nodes: 1323, Channels: 100, Relationships: 200
2025-06-17 01:38:45,997 - neo4j_client - INFO - Progress: 102/507 programs processed (20%) - Nodes: 1350, Channels: 102, Relationships: 204
2025-06-17 01:39:01,974 - neo4j_client - INFO - Progress: 125/507 programs processed (24%) - Nodes: 1649, Channels: 125, Relationships: 250
2025-06-17 01:39:03,469 - neo4j_client - INFO - Progress: 127/507 programs processed (25%) - Nodes: 1676, Channels: 127, Relationships: 254
2025-06-17 01:39:19,819 - neo4j_client - INFO - Progress: 150/507 programs processed (29%) - Nodes: 1986, Channels: 150, Relationships: 300
2025-06-17 01:39:22,021 - neo4j_client - INFO - Progress: 153/507 programs processed (30%) - Nodes: 2025, Channels: 153, Relationships: 306
2025-06-17 01:39:37,273 - neo4j_client - INFO - Progress: 175/507 programs processed (34%) - Nodes: 2311, Channels: 175, Relationships: 350
2025-06-17 01:39:39,452 - neo4j_client - INFO - Progress: 178/507 programs processed (35%) - Nodes: 2351, Channels: 178, Relationships: 356
2025-06-17 01:39:54,227 - neo4j_client - INFO - Progress: 200/507 programs processed (39%) - Nodes: 2638, Channels: 200, Relationships: 400
2025-06-17 01:39:56,394 - neo4j_client - INFO - Progress: 203/507 programs processed (40%) - Nodes: 2677, Channels: 203, Relationships: 406
2025-06-17 01:40:12,525 - neo4j_client - INFO - Progress: 225/507 programs processed (44%) - Nodes: 2964, Channels: 225, Relationships: 450
2025-06-17 01:40:15,351 - neo4j_client - INFO - Progress: 229/507 programs processed (45%) - Nodes: 3016, Channels: 229, Relationships: 458
2025-06-17 01:40:29,688 - neo4j_client - INFO - Progress: 250/507 programs processed (49%) - Nodes: 3290, Channels: 250, Relationships: 500
2025-06-17 01:40:32,402 - neo4j_client - INFO - Progress: 254/507 programs processed (50%) - Nodes: 3343, Channels: 254, Relationships: 508
2025-06-17 01:40:47,436 - neo4j_client - INFO - Progress: 275/507 programs processed (54%) - Nodes: 3619, Channels: 275, Relationships: 550
2025-06-17 01:40:50,227 - neo4j_client - INFO - Progress: 279/507 programs processed (55%) - Nodes: 3671, Channels: 279, Relationships: 558
2025-06-17 01:41:05,114 - neo4j_client - INFO - Progress: 300/507 programs processed (59%) - Nodes: 3944, Channels: 300, Relationships: 600
2025-06-17 01:41:08,527 - neo4j_client - INFO - Progress: 305/507 programs processed (60%) - Nodes: 4009, Channels: 305, Relationships: 610
2025-06-17 01:41:22,020 - neo4j_client - INFO - Progress: 325/507 programs processed (64%) - Nodes: 4270, Channels: 325, Relationships: 650
2025-06-17 01:41:25,497 - neo4j_client - INFO - Progress: 330/507 programs processed (65%) - Nodes: 4335, Channels: 330, Relationships: 660
2025-06-17 01:41:39,490 - neo4j_client - INFO - Progress: 350/507 programs processed (69%) - Nodes: 4595, Channels: 350, Relationships: 700
2025-06-17 01:41:42,852 - neo4j_client - INFO - Progress: 355/507 programs processed (70%) - Nodes: 4660, Channels: 355, Relationships: 710
2025-06-17 01:41:56,260 - neo4j_client - INFO - Progress: 375/507 programs processed (73%) - Nodes: 4921, Channels: 375, Relationships: 750
2025-06-17 01:42:00,716 - neo4j_client - INFO - Progress: 381/507 programs processed (75%) - Nodes: 5000, Channels: 381, Relationships: 762
2025-06-17 01:42:14,938 - neo4j_client - INFO - Progress: 400/507 programs processed (78%) - Nodes: 5247, Channels: 400, Relationships: 800
2025-06-17 01:42:18,919 - neo4j_client - INFO - Progress: 406/507 programs processed (80%) - Nodes: 5325, Channels: 406, Relationships: 812
2025-06-17 01:42:33,412 - neo4j_client - INFO - Progress: 425/507 programs processed (83%) - Nodes: 5576, Channels: 425, Relationships: 850
2025-06-17 01:42:39,491 - neo4j_client - INFO - Progress: 431/507 programs processed (85%) - Nodes: 5655, Channels: 431, Relationships: 862
2025-06-17 01:42:53,675 - neo4j_client - INFO - Progress: 450/507 programs processed (88%) - Nodes: 5902, Channels: 450, Relationships: 900
2025-06-17 01:42:58,525 - neo4j_client - INFO - Progress: 457/507 programs processed (90%) - Nodes: 5994, Channels: 457, Relationships: 914
2025-06-17 01:43:10,904 - neo4j_client - INFO - Progress: 475/507 programs processed (93%) - Nodes: 6229, Channels: 475, Relationships: 950
2025-06-17 01:43:15,518 - neo4j_client - INFO - Progress: 482/507 programs processed (95%) - Nodes: 6320, Channels: 482, Relationships: 964
2025-06-17 01:43:27,606 - neo4j_client - INFO - Progress: 500/507 programs processed (98%) - Nodes: 6555, Channels: 500, Relationships: 1000
2025-06-17 01:43:32,480 - neo4j_client - INFO - Progress: 507/507 programs processed (100%) - Nodes: 6646, Channels: 507, Relationships: 1014
2025-06-17 01:43:32,501 - neo4j_client - INFO - Final stats: {'nodes_created': 6646, 'programs_processed': 507, 'channels_created': 507, 'relationships_created': 1014}
2025-06-17 01:43:32,501 - __main__ - INFO - Ingestion completed. Stats: {'nodes_created': 6646, 'programs_processed': 507, 'channels_created': 507, 'relationships_created': 1014}
2025-06-17 01:43:32,501 - __main__ - INFO - Total programs processed: 507
2025-06-17 01:43:32,501 - __main__ - INFO - Ingestion stats: {'nodes_created': 6646, 'programs_processed': 507, 'channels_created': 507, 'relationships_created': 1014}
2025-06-17 01:43:33,023 - __main__ - INFO - Final database statistics: {
  "total_nodes": 1791,
  "total_programs": 507,
  "total_channels": 27,
  "total_relationships": 1014,
  "broadcasts_relationships": 507,
  "aired_on_relationships": 507,
  "node_labels": [
    {
      "labels": [
        "Program",
        "Channel",
        "Sport",
        "End",
        "Start",
        "Title",
        "Name",
        "Rating",
        "Synopsis",
        "Thumbnailuri",
        "Package",
        "Subgenre",
        "Islive"
      ]
    }
  ],
  "sample_nodes": [
    {
      "label": "Program",
      "count": 507
    },
    {
      "label": "Synopsis",
      "count": 281
    },
    {
      "label": "Thumbnailuri",
      "count": 240
    },
    {
      "label": "Title",
      "count": 234
    },
    {
      "label": "Start",
      "count": 202
    },
    {
      "label": "End",
      "count": 197
    },
    {
      "label": "Sport",
      "count": 35
    },
    {
      "label": "Subgenre",
      "count": 35
    },
    {
      "label": "Name",
      "count": 27
    },
    {
      "label": "Channel",
      "count": 27
    }
  ],
  "channel_program_counts": [
    {
      "channel_name": "Community Services Network",
      "channel_number": 490,
      "program_count": 36
    },
    {
      "channel_name": "SS Premier League",
      "channel_number": 203,
      "program_count": 34
    },
    {
      "channel_name": "SS WWE",
      "channel_number": 209,
      "program_count": 33
    },
    {
      "channel_name": "SS Variety 3",
      "channel_number": 208,
      "program_count": 32
    },
    {
      "channel_name": "SS Rugby",
      "channel_number": 211,
      "program_count": 31
    },
    {
      "channel_name": "SS Motorsport",
      "channel_number": 215,
      "program_count": 30
    },
    {
      "channel_name": "SuperSport PSL",
      "channel_number": 202,
      "program_count": 26
    },
    {
      "channel_name": "SuperSport School HD",
      "channel_number": 216,
      "program_count": 24
    },
    {
      "channel_name": "SS Cricket",
      "channel_number": 212,
      "program_count": 24
    },
    {
      "channel_name": "SS Football",
      "channel_number": 205,
      "program_count": 23
    }
  ]
}
2025-06-17 01:43:33,023 - __main__ - INFO - EPG Data Ingestion completed successfully
