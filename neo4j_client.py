from neo4j import GraphDatabase
from typing import List, Dict, Any
import logging
from models import EPGResponse, EPGProgram, Channel
from synopsis_parser import SynopsisParser


class Neo4jEPGClient:
    """Neo4j client for EPG data ingestion"""

    def __init__(self, uri: str, username: str, password: str, database: str = "neo4j"):
        self.driver = GraphDatabase.driver(uri, auth=(username, password))
        self.database = database
        self.logger = logging.getLogger(__name__)
        self.synopsis_parser = SynopsisParser()

    def close(self):
        """Close the database connection"""
        if self.driver:
            self.driver.close()

    def test_connection(self) -> bool:
        """Test database connection"""
        try:
            with self.driver.session(database=self.database) as session:
                result = session.run("RETURN 1 as test")
                return result.single()["test"] == 1
        except Exception as e:
            self.logger.error(f"Connection test failed: {e}")
            return False

    def create_constraints_and_indexes(self):
        """Create necessary constraints and indexes"""
        constraints_and_indexes = [
            # Constraints for uniqueness
            "CREATE CONSTRAINT channel_id_unique IF NOT EXISTS FOR (c:Channel) REQUIRE c.id IS UNIQUE",
            "CREATE CONSTRAINT sport_name_unique IF NOT EXISTS FOR (s:Sport) REQUIRE s.name IS UNIQUE",
            "CREATE CONSTRAINT genre_name_unique IF NOT EXISTS FOR (g:Genre) REQUIRE g.name IS UNIQUE",
            "CREATE CONSTRAINT package_name_unique IF NOT EXISTS FOR (p:Package) REQUIRE p.name IS UNIQUE",
            "CREATE CONSTRAINT team_name_unique IF NOT EXISTS FOR (t:Team) REQUIRE t.name IS UNIQUE",
            "CREATE CONSTRAINT tournament_name_unique IF NOT EXISTS FOR (t:Tournament) REQUIRE t.name IS UNIQUE",
            "CREATE CONSTRAINT venue_name_unique IF NOT EXISTS FOR (v:Venue) REQUIRE v.name IS UNIQUE",
            "CREATE CONSTRAINT location_name_unique IF NOT EXISTS FOR (l:Location) REQUIRE l.name IS UNIQUE",
            "CREATE CONSTRAINT person_name_unique IF NOT EXISTS FOR (p:Person) REQUIRE p.name IS UNIQUE",
            "CREATE CONSTRAINT age_group_name_unique IF NOT EXISTS FOR (a:AgeGroup) REQUIRE a.name IS UNIQUE",

            # Indexes for performance
            "CREATE INDEX channel_name_index IF NOT EXISTS FOR (c:Channel) ON (c.name)",
            "CREATE INDEX program_title_index IF NOT EXISTS FOR (p:Program) ON (p.title)",
            "CREATE INDEX program_start_time_index IF NOT EXISTS FOR (p:Program) ON (p.start_time)",
            "CREATE INDEX program_sport_index IF NOT EXISTS FOR (p:Program) ON (p.sport)",
            "CREATE INDEX team_name_index IF NOT EXISTS FOR (t:Team) ON (t.name)",
            "CREATE INDEX tournament_name_index IF NOT EXISTS FOR (t:Tournament) ON (t.name)",
            "CREATE INDEX venue_name_index IF NOT EXISTS FOR (v:Venue) ON (v.name)",
            "CREATE INDEX location_name_index IF NOT EXISTS FOR (l:Location) ON (l.name)",
            "CREATE INDEX person_name_index IF NOT EXISTS FOR (p:Person) ON (p.name)",
        ]

        with self.driver.session(database=self.database) as session:
            for query in constraints_and_indexes:
                try:
                    session.run(query)
                    self.logger.info(f"Executed: {query}")
                except Exception as e:
                    self.logger.warning(f"Failed to execute {query}: {e}")

    def clear_all_data(self):
        """Clear all EPG data from the database"""
        query = """
        MATCH (n)
        WHERE n:Channel OR n:Program OR n:Sport OR n:Genre OR n:Package
           OR n:Team OR n:Tournament OR n:Venue OR n:Location OR n:Person
           OR n:AgeGroup OR n:Episode
        DETACH DELETE n
        """
        with self.driver.session(database=self.database) as session:
            session.run(query)
            self.logger.info("Cleared all EPG data from database")

    def ingest_epg_data(self, epg_response: EPGResponse) -> Dict[str, int]:
        """
        Ingest EPG data into Neo4j

        Args:
            epg_response: EPG response containing programs

        Returns:
            Dictionary with counts of created nodes and relationships
        """
        total_programs = len(epg_response.programs)
        self.logger.info(f"Starting ingestion of {total_programs} programs")

        stats = {
            "channels": 0,
            "programs": 0,
            "sports": 0,
            "genres": 0,
            "packages": 0,
            "teams": 0,
            "tournaments": 0,
            "venues": 0,
            "locations": 0,
            "people": 0,
            "age_groups": 0,
            "episodes": 0,
            "relationships": 0
        }

        # Progress tracking variables
        processed_count = 0
        last_logged_percentage = -1
        log_interval = max(1, total_programs // 20)  # Log every 5% or at least every program

        with self.driver.session(database=self.database) as session:
            # Process each program
            for i, program in enumerate(epg_response.programs, 1):
                try:
                    # Parse synopsis for enhanced data
                    parsed_synopsis = self.synopsis_parser.parse_synopsis(program.synopsis)

                    # Create/update channel nodes
                    for channel in program.channel:
                        self._create_channel_node(session, channel)
                        stats["channels"] += 1

                    # Create program node
                    program_id = self._create_program_node(session, program)
                    stats["programs"] += 1

                    # Create sport node
                    self._create_sport_node(session, program.sport)
                    stats["sports"] += 1

                    # Create genre nodes
                    for genre in program.sub_genres:
                        self._create_genre_node(session, genre)
                        stats["genres"] += 1

                    # Create package nodes
                    for package in program.packages:
                        self._create_package_node(session, package)
                        stats["packages"] += 1

                    # Create enhanced nodes from synopsis
                    enhanced_stats = self._create_enhanced_nodes(session, parsed_synopsis)
                    for key, value in enhanced_stats.items():
                        stats[key] += value

                    # Create relationships (basic + enhanced)
                    rel_count = self._create_relationships(session, program, program_id, parsed_synopsis)
                    stats["relationships"] += rel_count

                    processed_count += 1

                    # Log progress at intervals or percentage milestones
                    percentage = (processed_count * 100) // total_programs

                    # Log every 5% or at specified intervals
                    if (processed_count % log_interval == 0 or
                        percentage != last_logged_percentage and percentage % 5 == 0 or
                        processed_count == total_programs):

                        self.logger.info(f"Progress: {processed_count}/{total_programs} programs processed ({percentage}%) - "
                                       f"Programs: {stats['programs']}, Relationships: {stats['relationships']}")
                        last_logged_percentage = percentage

                except Exception as e:
                    self.logger.error(f"Failed to process program {program.title}: {e}")
                    processed_count += 1

        self.logger.info(f"✅ Ingestion completed successfully! Processed {processed_count}/{total_programs} programs (100%)")
        self.logger.info(f"Final stats: {stats}")
        return stats

    def _create_channel_node(self, session, channel: Channel) -> str:
        """Create or update channel node"""
        query = """
        MERGE (c:Channel {id: $id})
        SET c.name = $name,
            c.channel_code = $channel_code,
            c.channel_number = $channel_number,
            c.stream = $stream,
            c.icon = $icon,
            c.mobile_icon = $mobile_icon,
            c.live_icon = $live_icon,
            c.square_icon = $square_icon,
            c.updated_at = datetime()
        RETURN c.id as id
        """
        result = session.run(query, {
            "id": channel.id,
            "name": channel.name,
            "channel_code": channel.channel_code,
            "channel_number": channel.channel_number,
            "stream": channel.stream,
            "icon": channel.icon,
            "mobile_icon": channel.mobile_icon,
            "live_icon": channel.live_icon,
            "square_icon": channel.square_icon
        })
        return result.single()["id"]

    def _create_program_node(self, session, program: EPGProgram) -> str:
        """Create program node"""
        # Create unique ID for program based on title, start time, and channel
        program_id = f"{program.title}_{program.start}_{program.channel[0].id if program.channel else 'unknown'}"

        query = """
        CREATE (p:Program {
            id: $id,
            title: $title,
            name: $name,
            sport: $sport,
            rating: $rating,
            synopsis: $synopsis,
            start_time: datetime($start_time),
            end_time: datetime($end_time),
            duration_minutes: $duration_minutes,
            is_live: $is_live,
            showmax: $showmax,
            thumbnail_uri: $thumbnail_uri,
            created_at: datetime()
        })
        RETURN p.id as id
        """
        result = session.run(query, {
            "id": program_id,
            "title": program.title,
            "name": program.name,
            "sport": program.sport,
            "rating": program.rating,
            "synopsis": program.synopsis,
            "start_time": program.start_datetime.isoformat(),
            "end_time": program.end_datetime.isoformat(),
            "duration_minutes": program.duration_minutes,
            "is_live": program.is_live,
            "showmax": program.showmax,
            "thumbnail_uri": program.thumbnail_uri or ""
        })
        return result.single()["id"]

    def _create_sport_node(self, session, sport_name: str):
        """Create or update sport node"""
        query = """
        MERGE (s:Sport {name: $name})
        SET s.updated_at = datetime()
        """
        session.run(query, {"name": sport_name})

    def _create_genre_node(self, session, genre_name: str):
        """Create or update genre node"""
        query = """
        MERGE (g:Genre {name: $name})
        SET g.updated_at = datetime()
        """
        session.run(query, {"name": genre_name})

    def _create_package_node(self, session, package_name: str):
        """Create or update package node"""
        query = """
        MERGE (p:Package {name: $name})
        SET p.updated_at = datetime()
        """
        session.run(query, {"name": package_name})

    def _create_enhanced_nodes(self, session, parsed_synopsis) -> Dict[str, int]:
        """Create enhanced nodes from parsed synopsis"""
        stats = {
            "teams": 0,
            "tournaments": 0,
            "venues": 0,
            "locations": 0,
            "people": 0,
            "age_groups": 0,
            "episodes": 0
        }

        # Create team nodes
        for team in parsed_synopsis.teams:
            self._create_team_node(session, team)
            stats["teams"] += 1

        # Create tournament node
        if parsed_synopsis.tournament:
            self._create_tournament_node(session, parsed_synopsis.tournament)
            stats["tournaments"] += 1

        # Create venue node
        if parsed_synopsis.venue:
            self._create_venue_node(session, parsed_synopsis.venue, parsed_synopsis.location)
            stats["venues"] += 1

        # Create location node
        if parsed_synopsis.location:
            self._create_location_node(session, parsed_synopsis.location)
            stats["locations"] += 1

        # Create person nodes
        for person in parsed_synopsis.people:
            self._create_person_node(session, person)
            stats["people"] += 1

        # Create age group node
        if parsed_synopsis.age_group:
            self._create_age_group_node(session, parsed_synopsis.age_group)
            stats["age_groups"] += 1

        # Create episode node
        if parsed_synopsis.episode_info:
            self._create_episode_node(session, parsed_synopsis.episode_info)
            stats["episodes"] += 1

        return stats

    def _create_team_node(self, session, team_name: str):
        """Create or update team node"""
        query = """
        MERGE (t:Team {name: $name})
        SET t.type = $type,
            t.updated_at = datetime()
        """
        session.run(query, {"name": team_name, "type": "team"})

    def _create_tournament_node(self, session, tournament_name: str):
        """Create or update tournament node"""
        query = """
        MERGE (t:Tournament {name: $name})
        SET t.type = $type,
            t.updated_at = datetime()
        """
        session.run(query, {"name": tournament_name, "type": "tournament"})

    def _create_venue_node(self, session, venue_name: str, location: str = None):
        """Create or update venue node"""
        query = """
        MERGE (v:Venue {name: $name})
        SET v.location = $location,
            v.updated_at = datetime()
        """
        session.run(query, {"name": venue_name, "location": location or ""})

    def _create_location_node(self, session, location_name: str):
        """Create or update location node"""
        query = """
        MERGE (l:Location {name: $name})
        SET l.type = $type,
            l.updated_at = datetime()
        """
        session.run(query, {"name": location_name, "type": "location"})

    def _create_person_node(self, session, person_name: str):
        """Create or update person node"""
        query = """
        MERGE (p:Person {name: $name})
        SET p.type = $type,
            p.updated_at = datetime()
        """
        session.run(query, {"name": person_name, "type": "person"})

    def _create_age_group_node(self, session, age_group_name: str):
        """Create or update age group node"""
        query = """
        MERGE (a:AgeGroup {name: $name})
        SET a.type = $type,
            a.updated_at = datetime()
        """
        session.run(query, {"name": age_group_name, "type": "age_group"})

    def _create_episode_node(self, session, episode_info: str):
        """Create or update episode node"""
        query = """
        MERGE (e:Episode {info: $info})
        SET e.updated_at = datetime()
        """
        session.run(query, {"info": episode_info})

    def _create_relationships(self, session, program: EPGProgram, program_id: str, parsed_synopsis=None) -> int:
        """Create all relationships for a program"""
        relationship_count = 0

        # BROADCASTS relationship between Channel and Program
        for channel in program.channel:
            query = """
            MATCH (c:Channel {id: $channel_id})
            MATCH (p:Program {id: $program_id})
            MERGE (c)-[r:BROADCASTS]->(p)
            SET r.start_time = datetime($start_time),
                r.end_time = datetime($end_time)
            """
            session.run(query, {
                "channel_id": channel.id,
                "program_id": program_id,
                "start_time": program.start_datetime.isoformat(),
                "end_time": program.end_datetime.isoformat()
            })
            relationship_count += 1

        # BELONGS_TO relationship between Program and Sport
        query = """
        MATCH (p:Program {id: $program_id})
        MATCH (s:Sport {name: $sport_name})
        MERGE (p)-[:BELONGS_TO]->(s)
        """
        session.run(query, {
            "program_id": program_id,
            "sport_name": program.sport
        })
        relationship_count += 1

        # HAS_GENRE relationships between Program and Genres
        for genre in program.sub_genres:
            query = """
            MATCH (p:Program {id: $program_id})
            MATCH (g:Genre {name: $genre_name})
            MERGE (p)-[:HAS_GENRE]->(g)
            """
            session.run(query, {
                "program_id": program_id,
                "genre_name": genre
            })
            relationship_count += 1

        # AVAILABLE_ON relationships between Program and Packages
        for package in program.packages:
            query = """
            MATCH (p:Program {id: $program_id})
            MATCH (pkg:Package {name: $package_name})
            MERGE (p)-[:AVAILABLE_ON]->(pkg)
            """
            session.run(query, {
                "program_id": program_id,
                "package_name": package
            })
            relationship_count += 1

        # Enhanced relationships from parsed synopsis
        if parsed_synopsis:
            relationship_count += self._create_enhanced_relationships(session, program_id, parsed_synopsis)

        return relationship_count

    def _create_enhanced_relationships(self, session, program_id: str, parsed_synopsis) -> int:
        """Create enhanced relationships from parsed synopsis"""
        relationship_count = 0

        # COMPETES_IN relationships between Teams and Tournament
        if parsed_synopsis.tournament:
            for team in parsed_synopsis.teams:
                query = """
                MATCH (t:Team {name: $team_name})
                MATCH (tournament:Tournament {name: $tournament_name})
                MERGE (t)-[:COMPETES_IN]->(tournament)
                """
                session.run(query, {
                    "team_name": team,
                    "tournament_name": parsed_synopsis.tournament
                })
                relationship_count += 1

        # COMPETES_AGAINST relationships between Teams
        if len(parsed_synopsis.teams) == 2:
            team1, team2 = parsed_synopsis.teams
            query = """
            MATCH (t1:Team {name: $team1_name})
            MATCH (t2:Team {name: $team2_name})
            MATCH (p:Program {id: $program_id})
            MERGE (t1)-[r:COMPETES_AGAINST]->(t2)
            SET r.program_id = $program_id,
                r.date = p.start_time
            """
            session.run(query, {
                "team1_name": team1,
                "team2_name": team2,
                "program_id": program_id
            })
            relationship_count += 1

        # PLAYS_AT relationship between Program and Venue
        if parsed_synopsis.venue:
            query = """
            MATCH (p:Program {id: $program_id})
            MATCH (v:Venue {name: $venue_name})
            MERGE (p)-[:PLAYS_AT]->(v)
            """
            session.run(query, {
                "program_id": program_id,
                "venue_name": parsed_synopsis.venue
            })
            relationship_count += 1

        # LOCATED_IN relationship between Venue and Location
        if parsed_synopsis.venue and parsed_synopsis.location:
            query = """
            MATCH (v:Venue {name: $venue_name})
            MATCH (l:Location {name: $location_name})
            MERGE (v)-[:LOCATED_IN]->(l)
            """
            session.run(query, {
                "venue_name": parsed_synopsis.venue,
                "location_name": parsed_synopsis.location
            })
            relationship_count += 1

        # FEATURES relationships between Program and People
        for person in parsed_synopsis.people:
            query = """
            MATCH (p:Program {id: $program_id})
            MATCH (person:Person {name: $person_name})
            MERGE (p)-[:FEATURES]->(person)
            """
            session.run(query, {
                "program_id": program_id,
                "person_name": person
            })
            relationship_count += 1

        # HAS_AGE_GROUP relationship between Program and AgeGroup
        if parsed_synopsis.age_group:
            query = """
            MATCH (p:Program {id: $program_id})
            MATCH (a:AgeGroup {name: $age_group_name})
            MERGE (p)-[:HAS_AGE_GROUP]->(a)
            """
            session.run(query, {
                "program_id": program_id,
                "age_group_name": parsed_synopsis.age_group
            })
            relationship_count += 1

        # PART_OF_SERIES relationship between Program and Episode
        if parsed_synopsis.episode_info:
            query = """
            MATCH (p:Program {id: $program_id})
            MATCH (e:Episode {info: $episode_info})
            MERGE (p)-[:PART_OF_SERIES]->(e)
            """
            session.run(query, {
                "program_id": program_id,
                "episode_info": parsed_synopsis.episode_info
            })
            relationship_count += 1

        # INVOLVES relationships between Teams and Program
        for team in parsed_synopsis.teams:
            query = """
            MATCH (p:Program {id: $program_id})
            MATCH (t:Team {name: $team_name})
            MERGE (p)-[:INVOLVES]->(t)
            """
            session.run(query, {
                "program_id": program_id,
                "team_name": team
            })
            relationship_count += 1

        return relationship_count

    def get_statistics(self) -> Dict[str, Any]:
        """Get database statistics"""
        queries = {
            "total_channels": "MATCH (c:Channel) RETURN count(c) as count",
            "total_programs": "MATCH (p:Program) RETURN count(p) as count",
            "total_sports": "MATCH (s:Sport) RETURN count(s) as count",
            "total_genres": "MATCH (g:Genre) RETURN count(g) as count",
            "total_packages": "MATCH (pkg:Package) RETURN count(pkg) as count",
            "total_teams": "MATCH (t:Team) RETURN count(t) as count",
            "total_tournaments": "MATCH (t:Tournament) RETURN count(t) as count",
            "total_venues": "MATCH (v:Venue) RETURN count(v) as count",
            "total_locations": "MATCH (l:Location) RETURN count(l) as count",
            "total_people": "MATCH (p:Person) RETURN count(p) as count",
            "total_age_groups": "MATCH (a:AgeGroup) RETURN count(a) as count",
            "total_episodes": "MATCH (e:Episode) RETURN count(e) as count",
            "total_relationships": "MATCH ()-[r]->() RETURN count(r) as count",
            "live_programs": "MATCH (p:Program {is_live: true}) RETURN count(p) as count",
            "programs_by_sport": """
                MATCH (p:Program)-[:BELONGS_TO]->(s:Sport)
                RETURN s.name as sport, count(p) as count
                ORDER BY count DESC
                LIMIT 10
            """,
            "channels_with_most_programs": """
                MATCH (c:Channel)-[:BROADCASTS]->(p:Program)
                RETURN c.name as channel, count(p) as program_count
                ORDER BY program_count DESC
                LIMIT 10
            """,
            "most_active_teams": """
                MATCH (t:Team)-[:COMPETES_IN]->(tournament:Tournament)
                RETURN t.name as team, count(tournament) as tournaments
                ORDER BY tournaments DESC
                LIMIT 10
            """,
            "popular_venues": """
                MATCH (v:Venue)<-[:PLAYS_AT]-(p:Program)
                RETURN v.name as venue, v.location as location, count(p) as programs
                ORDER BY programs DESC
                LIMIT 10
            """,
            "tournaments_by_programs": """
                MATCH (t:Tournament)<-[:COMPETES_IN]-(team:Team)
                RETURN t.name as tournament, count(DISTINCT team) as teams
                ORDER BY teams DESC
                LIMIT 10
            """
        }

        stats = {}
        with self.driver.session(database=self.database) as session:
            for key, query in queries.items():
                try:
                    result = session.run(query)
                    if key in ["programs_by_sport", "channels_with_most_programs", "most_active_teams", "popular_venues", "tournaments_by_programs"]:
                        stats[key] = [dict(record) for record in result]
                    else:
                        stats[key] = result.single()["count"]
                except Exception as e:
                    self.logger.error(f"Failed to get {key}: {e}")
                    stats[key] = 0 if key not in ["programs_by_sport", "channels_with_most_programs", "most_active_teams", "popular_venues", "tournaments_by_programs"] else []

        return stats

    def search_programs(self, search_term: str, limit: int = 20) -> List[Dict[str, Any]]:
        """Search programs by title or synopsis"""
        query = """
        MATCH (p:Program)
        WHERE toLower(p.title) CONTAINS toLower($search_term)
           OR toLower(p.synopsis) CONTAINS toLower($search_term)
        RETURN p.title as title, p.synopsis as synopsis,
               p.start_time as start_time, p.sport as sport
        ORDER BY p.start_time DESC
        LIMIT $limit
        """

        with self.driver.session(database=self.database) as session:
            result = session.run(query, {"search_term": search_term, "limit": limit})
            return [dict(record) for record in result]

    def get_channel_schedule(self, channel_id: str, date: str) -> List[Dict[str, Any]]:
        """Get schedule for a specific channel on a specific date"""
        query = """
        MATCH (c:Channel {id: $channel_id})-[:BROADCASTS]->(p:Program)
        WHERE date(p.start_time) = date($date)
        RETURN p.title as title, p.start_time as start_time,
               p.end_time as end_time, p.sport as sport, p.is_live as is_live
        ORDER BY p.start_time
        """

        with self.driver.session(database=self.database) as session:
            result = session.run(query, {"channel_id": channel_id, "date": date})
            return [dict(record) for record in result]