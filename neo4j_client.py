from neo4j import GraphDatabase
from typing import List, Dict, Any
import logging


class Neo4jEPGClient:
    """Simplified Neo4j client for EPG data ingestion"""

    def __init__(self, uri: str, username: str, password: str, database: str = "neo4j"):
        self.driver = GraphDatabase.driver(uri, auth=(username, password))
        self.database = database
        self.logger = logging.getLogger(__name__)

    def close(self):
        """Close the database connection"""
        if self.driver:
            self.driver.close()

    def test_connection(self) -> bool:
        """Test database connection"""
        try:
            with self.driver.session(database=self.database) as session:
                result = session.run("RETURN 1 as test")
                return result.single()["test"] == 1
        except Exception as e:
            self.logger.error(f"Connection test failed: {e}")
            return False

    def create_constraints_and_indexes(self):
        """Create basic constraints and indexes"""
        constraints_and_indexes = [
            # Basic constraints for common node types
            "CREATE CONSTRAINT program_id_unique IF NOT EXISTS FOR (p:Program) REQUIRE p.id IS UNIQUE",
            "CREATE CONSTRAINT channel_id_unique IF NOT EXISTS FOR (c:Channel) REQUIRE c.id IS UNIQUE",

            # Basic indexes for performance
            "CREATE INDEX program_title_index IF NOT EXISTS FOR (p:Program) ON (p.title)",
            "CREATE INDEX channel_name_index IF NOT EXISTS FOR (c:Channel) ON (c.name)",
        ]

        with self.driver.session(database=self.database) as session:
            for query in constraints_and_indexes:
                try:
                    session.run(query)
                    self.logger.info(f"Executed: {query}")
                except Exception as e:
                    self.logger.warning(f"Failed to execute {query}: {e}")

    def clear_all_data(self):
        """Clear all data from the database"""
        query = "MATCH (n) DETACH DELETE n"
        with self.driver.session(database=self.database) as session:
            session.run(query)
            self.logger.info("Cleared all data from database")

    def ingest_epg_data(self, epg_data: List[Dict[str, Any]]) -> Dict[str, int]:
        """
        Simplified EPG data ingestion - directly inject response data as nodes

        Args:
            epg_data: Raw EPG response data (list of program dictionaries)

        Returns:
            Dictionary with counts of created nodes
        """
        total_programs = len(epg_data)
        self.logger.info(f"Starting simplified ingestion of {total_programs} programs")

        stats = {"nodes_created": 0, "programs_processed": 0}

        # Progress tracking
        processed_count = 0
        last_logged_percentage = -1
        log_interval = max(1, total_programs // 20)  # Log every 5%

        with self.driver.session(database=self.database) as session:
            for program_data in epg_data:
                try:
                    # Create nodes directly from the program data
                    nodes_created = self._create_nodes_from_data(session, program_data, processed_count)
                    stats["nodes_created"] += nodes_created
                    stats["programs_processed"] += 1

                    processed_count += 1

                    # Log progress
                    percentage = (processed_count * 100) // total_programs
                    if (processed_count % log_interval == 0 or
                        percentage != last_logged_percentage and percentage % 5 == 0 or
                        processed_count == total_programs):

                        self.logger.info(f"Progress: {processed_count}/{total_programs} programs processed ({percentage}%) - "
                                       f"Nodes created: {stats['nodes_created']}")
                        last_logged_percentage = percentage

                except Exception as e:
                    self.logger.error(f"Failed to process program: {e}")
                    processed_count += 1

        self.logger.info(f"✅ Ingestion completed! Processed {processed_count}/{total_programs} programs (100%)")
        self.logger.info(f"Final stats: {stats}")
        return stats

    def _create_nodes_from_data(self, session, program_data: Dict[str, Any], program_index: int) -> int:
        """
        Create nodes directly from program data - key as label, value as property

        Args:
            session: Neo4j session
            program_data: Raw program data dictionary
            program_index: Index for unique program identification

        Returns:
            Number of nodes created
        """
        nodes_created = 0

        # Create a unique program ID
        program_id = f"program_{program_index}_{program_data.get('title', 'unknown').replace(' ', '_')}"

        # Create main Program node with all program properties
        program_query = """
        CREATE (p:Program {
            id: $program_id,
            title: $title,
            name: $name,
            sport: $sport,
            rating: $rating,
            synopsis: $synopsis,
            start_time: $start_time,
            end_time: $end_time,
            is_live: $is_live,
            showmax: $showmax,
            thumbnail_uri: $thumbnail_uri,
            created_at: datetime()
        })
        """

        session.run(program_query, {
            "program_id": program_id,
            "title": program_data.get("title", ""),
            "name": program_data.get("name", ""),
            "sport": program_data.get("sport", ""),
            "rating": program_data.get("rating", ""),
            "synopsis": program_data.get("synopsis", ""),
            "start_time": program_data.get("start", ""),
            "end_time": program_data.get("end", ""),
            "is_live": program_data.get("isLive", False),
            "showmax": program_data.get("showmax", False),
            "thumbnail_uri": program_data.get("thumbnailUri", "")
        })
        nodes_created += 1

        # Create nodes for each key-value pair in the data
        for key, value in program_data.items():
            if key in ["channel", "packages", "subGenres"]:  # Handle list values
                if isinstance(value, list):
                    for item in value:
                        if isinstance(item, dict):  # Channel objects
                            for sub_key, sub_value in item.items():
                                if sub_value:  # Only create nodes for non-empty values
                                    self._create_simple_node(session, sub_key.capitalize(), str(sub_value))
                                    nodes_created += 1
                        else:  # Simple list items (packages, genres)
                            if item:  # Only create nodes for non-empty values
                                self._create_simple_node(session, key.capitalize().rstrip('s'), str(item))
                                nodes_created += 1
            elif value and not isinstance(value, (list, dict)):  # Simple key-value pairs
                self._create_simple_node(session, key.capitalize(), str(value))
                nodes_created += 1

        return nodes_created

    def _create_simple_node(self, session, label: str, value: str):
        """Create a simple node with label and value"""
        # Clean up label name
        clean_label = label.replace(" ", "").replace("-", "").replace("_", "")

        query = f"""
        MERGE (n:{clean_label} {{value: $value}})
        SET n.created_at = datetime()
        """

        session.run(query, {"value": value})


    def get_statistics(self) -> Dict[str, Any]:
        """Get simplified database statistics"""
        queries = {
            "total_nodes": "MATCH (n) RETURN count(n) as count",
            "total_programs": "MATCH (p:Program) RETURN count(p) as count",
            "node_labels": """
                CALL db.labels() YIELD label
                RETURN collect(label) as labels
            """,
            "sample_nodes": """
                MATCH (n)
                RETURN labels(n)[0] as label, count(n) as count
                ORDER BY count DESC
                LIMIT 10
            """
        }

        stats = {}
        with self.driver.session(database=self.database) as session:
            for key, query in queries.items():
                try:
                    result = session.run(query)
                    if key in ["node_labels", "sample_nodes"]:
                        stats[key] = [dict(record) for record in result]
                    else:
                        stats[key] = result.single()["count"]
                except Exception as e:
                    self.logger.error(f"Failed to get {key}: {e}")
                    stats[key] = 0 if key not in ["node_labels", "sample_nodes"] else []

        return stats

    def search_programs(self, search_term: str, limit: int = 20) -> List[Dict[str, Any]]:
        """Search programs by title or synopsis"""
        query = """
        MATCH (p:Program)
        WHERE toLower(p.title) CONTAINS toLower($search_term)
           OR toLower(p.synopsis) CONTAINS toLower($search_term)
        RETURN p.title as title, p.synopsis as synopsis,
               p.start_time as start_time, p.sport as sport
        ORDER BY p.start_time DESC
        LIMIT $limit
        """

        with self.driver.session(database=self.database) as session:
            result = session.run(query, {"search_term": search_term, "limit": limit})
            return [dict(record) for record in result]